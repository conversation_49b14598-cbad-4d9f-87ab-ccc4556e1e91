import React, { useEffect } from 'react'
import styles from './ClaimOrderRightWindow.module.scss'
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import { format4DigitAmount, formatToTwoDecimalPlaces, getChannelWindow, priceUnits, purchaseOrder, useChatWithVendorStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { useLocation, useNavigate } from 'react-router-dom';
import { routes } from 'src/renderer2/common';
import PdfMakePage from 'src/renderer2/pages/PdfMake/pdfMake';
import { descriptionLines, getOtherDescriptionLines } from 'src/renderer2/utility/pdfUtils';
import clsx from 'clsx';
import ChatWithVendor from '../ChatWithVendor/ChatWithVendor';

const ClaimOrderRightWindow = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const channelWindow = getChannelWindow();
    const { props } = useRightWindowStore();
    const { isRequiredSellerSettingsFilled } = useGlobalStore();
    const {
        orderDetail, availableTime, totalOrderValue, disableNextButton, disableOnclick, dialogRef, openSubmitApp,
        handleClickOpen, nextPage, setOpenReminderYouAreAlmostTherePopup, setDisableOnclick, setIsReminderPopup, handleUnDeleteClick, componentType
    } = props || {};
    const { setPoNumber, setChannelName, setCompanyName, poNumber } = useChatWithVendorStore();

    const isOrderManagementPage = componentType === 'ORDER' || location.pathname === routes.orderManagementPage;
    const isPreviewOrderPage = location.pathname === routes.previewOrderPage;
    const isDeleteOrderPage = location.pathname === routes.deleteOrderPage;

    useEffect(() => {
        if (isOrderManagementPage && orderDetail?.seller_po_number !== poNumber) {
            const channelName = 'S' + orderDetail?.seller_po_number.substring(1, orderDetail?.seller_po_number?.length);
            setPoNumber(orderDetail?.buyer_internal_po)
            setChannelName(channelName)
            setCompanyName(orderDetail?.buyer_company_name ? orderDetail.buyer_company_name : 'Buyer Company Name')
        }
    }, [orderDetail])


    const handleExportPDfClick = ($event) => {
        $event.stopPropagation();
        if (isRequiredSellerSettingsFilled) {
            setOpenReminderYouAreAlmostTherePopup(false);
            setDisableOnclick(false)
        } else {
            setIsReminderPopup(false);
            setDisableOnclick(true);
            setOpenReminderYouAreAlmostTherePopup(true);
        }
    }

    const calculateSellerLineWeight = (data) => {
        return formatToTwoDecimalPlaces(data.total_weight)
    }

    const getCartItems = () => {
        const { items } = orderDetail;
        const formattedItems = items.map((item, index) => ({
            description: descriptionLines(item.description),
            otherDescription: getOtherDescriptionLines(item.description),
            product_tag: item.product_tag,
            domesticMaterialOnly: item.domestic_material_only ? '\nDomestic (USA) Material Only' : '',
            qty: formatToTwoDecimalPlaces(item.qty),
            qty_unit: item.qty_unit,
            price_unit: item.price_unit,
            price: item.price_unit.toLowerCase() === priceUnits.lb ? format4DigitAmount(item.seller_price_per_unit) : formatToTwoDecimalPlaces(item.seller_price_per_unit),
            line_weight: calculateSellerLineWeight(item),
            extended: formatToTwoDecimalPlaces(item.seller_line_total),
            line_weight_unit: "Lb",
            line_no: index,
            po_line: index.toString(),
            total_weight: item.total_weight
        }));
        return formattedItems
    }

    return (<>
        <div className={styles.claimOrderRightWindow}>
            {/* <div className={clsx(openSubmitApp && styles.blurredContainer)}></div>   */}
            <div className={styles.topSection}>
                <div className={styles.summaryCard}>
                <div className={styles.summaryCardContent}>
                    <div className={styles.summaryRow}>
                        <div>Material Total</div>
                        <div>$ {formatToTwoDecimalPlaces(orderDetail?.seller_po_price)}</div>
                    </div>
                    <div className={clsx(styles.summaryRow, styles.summaryRowSalesTax)}>
                        <div>Sales Tax</div>
                        <div>{parseFloat(orderDetail?.seller_sales_tax) > 0 ? `$ ${formatToTwoDecimalPlaces(orderDetail?.seller_sales_tax)}` : 'Exempt'}</div>
                    </div>
                </div>
                <div className={clsx(styles.summaryTotal)}>
                    <div>Total Purchase</div>
                    <div>$ {formatToTwoDecimalPlaces(totalOrderValue)}</div>
                </div>
            </div>
            <div className={styles.infoText}>
                { isOrderManagementPage ?
                    "Total Sale Amount includes cost of material and delivery."
                    :
                    "After clicking “Accept Order,” the next screen will be your order confirmation. Additionally, we will send you a purchase order for your records."
                }
            </div>
                    <div>
                    {orderDetail?.claimed_by === purchaseOrder.pending  && (isPreviewOrderPage || isDeleteOrderPage) && <div className={styles.availableToClaim}>AVAILABLE TO CLAIM @ {availableTime}</div>}
                    {
                        ( !isPreviewOrderPage && !isOrderManagementPage) && 
                      <>
                    {(orderDetail?.is_order_hidden === true || isDeleteOrderPage) ?
                        <button className={styles.undeleteButton} onClick={handleUnDeleteClick}>
                            UNDELETE
                        </button>
                        :
                        <button className={styles.acceptButton} onClick={handleClickOpen}>
                            ACCEPT ORDER
                        </button>
                    }
                      </>
                    }

                </div>
            </div>

            
        </div>

        {
                (location.pathname === routes.orderManagementPage && orderDetail && orderDetail?.buyer_id) &&
                    <ChatWithVendor
                        close={() => { }}
                        userName={orderDetail?.buyer_name || 'Buyer Name'}
                    />
            }
      </>

    )
}

export default ClaimOrderRightWindow