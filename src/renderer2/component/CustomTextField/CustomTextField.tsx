import { emojiRemoverRegex, formatPhoneNumber } from "@bryzos/giss-ui-library";

import clsx from 'clsx'
import styles from './CustomTextField.module.scss'
import { UseFormRegisterReturn, UseFormSetError } from "react-hook-form";
import { useState } from "react";
import { formatPhoneNumberWithHyphen } from "src/renderer2/helper";

type CustomTextFieldProps = {
    type?: string,
    register?: UseFormRegisterReturn,
    autoFocus?: boolean,
    onFocus?: Function,
    onChange?: Function,
    placeholder: string,
    onBlur?: Function,
    inputRef?: any,
    className?:any,
    errorInput?:any,
    maxLength?: number | undefined,
    readOnly?: boolean | undefined,
    mode?: string,
    setError?: React.Dispatch<React.SetStateAction<boolean>>,
    disabled?: boolean,
    tabIndex?: number,
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void,
    id?: string,
    pattern?: string,
    value?: string,
    lastFocusable?: string
}

const CustomTextField: React.FC<CustomTextFieldProps> =({type='text', register={}, autoFocus, onFocus, onChange, placeholder, onBlur, inputRef, className, errorInput, maxLength, readOnly, mode, setError, disabled, tabIndex, onKeyDown, id , pattern, value , dataHoverVideoId , lastFocusable}) => {


    const handleOnFocus = (e) => {
        if(onFocus) onFocus(e);
    }

    const handleOnChange = (e) => {
        e.target.value = e.target.value.replace(emojiRemoverRegex, '');
        if(setError && type === 'email'){
            if(e.target.value.match(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)){
                setError(true);
            }else{
                setError(false);
            }
        }
        if(mode === 'wholeNumber') e.target.value = e.target.value.replace(/\D/g, '');
        if(mode === 'number') e.target.value = e.target.value.replace(/[^\d.]/g, '');
        if(mode === 'phoneNumber') e.target.value = formatPhoneNumber(e.target.value);
        if(mode === 'phoneNumberHyphen') e.target.value = formatPhoneNumberWithHyphen(e.target.value);
        if(mode === 'currency') e.target.value = e.target.value.replace(/\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g, ",");
        if(mode === 'commaNumber') {
            e.target.value = e.target.value.replace(/\D/g, '');
            e.target.value = e.target.value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }
        if (mode === 'maskedWholeNumber') {
            if (e.target.value?.includes('x')) {
              // Clear the value completely
              e.target.value = '';
            } else {
              // Behave like wholeNumber mode
              e.target.value = e.target.value.replace(/\D/g, '');
            }
          }        
        if(register?.onChange) register.onChange(e);
        if(onChange) onChange(e);
    }

    const handleOnBlur = (e) => {
        e.target.value = e.target.value.trim();
        if(register?.onBlur) register.onBlur(e);
        if(onBlur) onBlur(e);
    }

    const handleInputRef = (e: HTMLInputElement | null) => {
        if(inputRef) inputRef(e);
        if(register?.ref && e) register.ref(e);
    }

    return(
        <>
            <input
                {...(register || {})}
                autoFocus={autoFocus}
                onFocus={handleOnFocus}
                onChange={handleOnChange}
                onBlur={handleOnBlur}
                placeholder={placeholder}
                type={type}
                ref={handleInputRef}
                maxLength={maxLength}
                readOnly={readOnly}
                className={clsx(styles.input, !!errorInput && styles.inputError, className && className)}
                disabled={disabled}
                tabIndex={tabIndex}
                onKeyDown={onKeyDown}
                id={id}
                pattern={pattern}
                value={value}
                data-hover-video-id={dataHoverVideoId || ""}
                data-last-focusable={lastFocusable || undefined}
            />
        </>
    )
}
    
export default CustomTextField;