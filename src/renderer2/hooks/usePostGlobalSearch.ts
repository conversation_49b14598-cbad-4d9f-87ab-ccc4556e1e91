import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { useGlobalStore, commomKeys } from "@bryzos/giss-ui-library";
import useDialogStore from "src/renderer2/component/DialogPopup/DialogStore";
import { setIntervalPromise } from "../helper";
import { fillFoundText } from "../pages/GlobalSearchField/SearchUtility";

type SearchCriteria =
  | "all"
  | "instant_pricing"
  | "order_management"
  | "quote"
  | "purchasing";

export interface GlobalSearchPayload {
  data: {
    search_term: string;
    search_criterea: SearchCriteria;
  };
}

export interface GlobalSearchResultItem {
  id: string;
  order_type?: "QUOTE" | "PO" | "preview_order" | "claim_order";
  address?: string;
  city?: string;
  state_code?: string;
  zip?: string;
  buyer_internal_PO?: string;
  lines_text?: string;
  delivery_date?: string;
  created_date?: string;
  grand_total?: string;
  price_unit?: string;
  score?: number;
  found_text?: string;
  search_type?: string;
  // you can extend if backend returns more
}

export interface GlobalSearchResponse {
  success: boolean;
  results: {
    pricing: GlobalSearchResultItem[];
    quote: GlobalSearchResultItem[];
    purchase: GlobalSearchResultItem[];
    order: GlobalSearchResultItem[];
    preview_order: GlobalSearchResultItem[];
    claim_order: GlobalSearchResultItem[];
  };
  message?: string;
}


const usePostGlobalSearch = () => {
  const { /* not used here but available */ } = useGlobalStore();
  const { showCommonDialog, resetDialogStore }: any = useDialogStore();

  return useMutation(async (payload: GlobalSearchPayload) => {
    try {
        
      const url = `${import.meta.env.VITE_API_SEARCH_SERVICE}/api/v1/search`;
      //payload.data.search_criteria = "all";
      const response = await axios.post<GlobalSearchResponse>(url, payload);

      const data = response?.data.data;
      data.search_term = payload.data.search_term;
      if (!data) return null;

        // const data = await setIntervalPromise(() => {
        //     return dummyData;
        //   }, 300
        // )
      // If your API sometimes encodes errors as objects, mirror your pattern:
      if (
        typeof (data as any) === "object" &&
        "data" in (data as any) &&
        typeof (data as any).data === "object" &&
        "error_message" in (data as any).data
      ) {
        showCommonDialog(
          null,
          commomKeys.errorContent,
          commomKeys.actionStatus.error,
          resetDialogStore,
          [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
        );
        return null;
      }

      //before ruturning data look for found_text and if not available add it by breaking lines_text into words and find the first match
      
      const retVal = fillFoundText(data);
      return retVal;
    } catch (error: any) {
      showCommonDialog(
        null,
        commomKeys.errorContent,
        commomKeys.actionStatus.error,
        resetDialogStore,
        [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
      );
      throw new Error(error?.message || "Search failed");
    }
  });
};

export default usePostGlobalSearch;
