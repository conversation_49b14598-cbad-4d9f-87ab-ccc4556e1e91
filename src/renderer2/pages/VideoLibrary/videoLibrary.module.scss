.videoPlayerMain {
    background-color: #191a20;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .videoNoLongerAvailable {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #000;
        color: #fff;
        font-size: 16px;
        width: calc(100% - 40px);
        height: 300px;
        margin: 0 20px 20px 20px;
    }

    .videoMainContainer {
        width: 100%;
        padding: 16px 40px 22px;
        box-shadow: 0 -16px 15.1px -11px rgba(0, 0, 0, 0.6);
        border-style: solid;
        border-width: 1px;
        border-image-source: linear-gradient(180deg, #fff -180%, #1a1b21 24%);
        border-image-slice: 1;
        background-color: #191a20;
        overflow-y: scroll;
        overflow-x: hidden;
        &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .videoContainer {
            margin-bottom: 24px;
    
            .targetedTag {
                font-family: Syncopate;
                font-size: 18.5px;
                font-weight: bold;
                line-height: normal;
                text-align: left;
                color: #fff;
                text-transform: uppercase;
                margin: 20px 0px 8px 0px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
            }
        }
        .videoPlayerMainTitle{
            font-family: Inter;
            font-size: 18px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: 0.72px;
            text-align: left;
            color: #fff;
        }
        .videoPlayerDescription{
            font-family: Inter;
            font-size: 12px;
            font-weight: 300;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: 0.48px;
            text-align: left;
            color: #c3c4ca;
            margin-bottom: 16px;
            margin-top: 4px;
        }
        .videoPlayerDescriptionBottom{
            font-family: Inter;
            font-size: 14px;
            font-weight: 300;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: 0.56px;
            text-align: left;
            color: #c3c4ca;
            margin-bottom: 16px;
            margin-top: 16px;
        }
        .videoPlayerShareButton{
            display: flex;
            justify-content: right;
            margin-bottom: 16px;
            gap: 12px;
            .shareVideoButton{
                height: 36px;
                padding: 6px 12px;
                border-radius: 5000px;
                background-color: #c3c4ca;
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: left;
                color: #0f0f14;
                cursor: pointer;
                border: none;
                outline: none;
                transition: all 0.1s;
                &:hover{
                    background-color: #fff;
                }
                &:focus{
                    background-color: #fff;
                }
            }
            .addWatchlistButton{
                height: 36px;
                padding: 6px 12px;
                border-radius: 5000px;
                background-color: rgba(255, 255, 255, 0.04);
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: left;
                color: #9b9eac;
                cursor: pointer;
                border: none;
                outline: none;
                transition: all 0.1s;
                display: flex;
                align-items: center;
                gap: 8px;
                svg{
                    width: 24px;
                    height: 24px;
                    path{
                        fill-opacity: 1;
                        fill: #9b9eac;
                    }
                }
                &:hover{
                    color: #fff;
                    svg{
                        path{
                            fill: #fff;
                        }
                    }
                }
                &:focus{
                    color: #fff;
                    svg{
                        path{
                            fill: #fff;
                        }
                    }
                }
            }
        }
    }

    .descriptionVideo {
        font-family: Inter;
        font-size: 14px;
        font-weight: normal;
        line-height: normal;
        text-align: left;
        color: #fff;
        margin-bottom: 8px;

        button {
            font-family: Inter;
            font-size: 14px;
            color: #16b9ff;
            margin-left: 3px;
        }
    }

    .videoPlayerTitleMain {
        padding: 24px 16px 20px 16px;

        .videoPlayerTitle {
            font-family: Syncopate;
            font-size: 24px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: 2.4px;
            text-align: center;
            color: #fff;
            margin-bottom: 8px;
        }

        p {
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: normal;
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
        }
    }

    .thumbnailMain {
        width: 100%;
    }

    .episodesThumbnailSection {
        overflow: auto;

        .episodesTitle {

            span {
                font-family: Syncopate;
                font-size: 14.5px;
                font-weight: bold;
                line-height: 1.4;
                color: #fff;
                margin-right: 4px;
                display: inline-block;
                text-transform: uppercase;
            }

            .tagSubtitle {
                font-family: Inter;
                margin-left: 5px;
                font-size: 12px;
                font-weight: normal;
                text-transform: none;
                color: rgba(255, 255, 255, 0.7);
            }
        }

        .videoPlayerThumbFlex {
            display: flex;
            column-gap: 12px;
            overflow-x: auto;
            padding-bottom: 6px;
            margin-bottom: 10px;

            &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }
        }

        .videoThumb {
            width: 140px;
            border-radius: 4px;

            .videoThumbBox {
                width: 140px;
                height: 120px;
                flex-grow: 0;
                border-radius: 4px;
                background-color: rgba(0, 0, 0, 0.3);
                margin: 12px 0px;
                position: relative;
                transition: all 0.1s;
                border: solid 1px transparent;

                &.selectedVideo {
                    border: solid 1px rgba(255, 255, 255, 0.7);

                    img {
                        opacity: 0.4;
                    }

                    .overlay {
                        background-color: rgba(255, 255, 255, 0.4);
                        pointer-events: none;

                        .nowPlatingtxt {
                            font-family: Syncopate;
                            font-size: 12px;
                            font-weight: 600;
                            color: #fff;
                            text-transform: uppercase;
                        }
                    }

                    &:hover {
                        border: solid 1px rgba(255, 255, 255, 0.7);
                    }
                }

                .videoThumbBoxInnerContent {
                    width: 100%;
                    height: 100%;
                }

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: fill;
                    border-radius: 4px;
                }

                .overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.3);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                }

                &:hover {
                    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.25);
                    border: solid 1px #16b9ff;
                }

                .VideoPlayIcon {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    cursor: pointer;
                    z-index: 99;

                    svg {
                        border-radius: 50%;
                        box-shadow: inset -0.4px 0.4px 0.4px -0.9px rgba(255, 255, 255, 0.35);
                        border-style: solid;
                        border-width: 0.6px;
                        border-image-source: linear-gradient(202deg, #fff 24%, rgba(255, 255, 255, 0) 15%);
                        background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.25), rgba(0, 0, 0, 0.25)), linear-gradient(202deg, rgb(255 255 255 / 44%) 92%, rgba(255, 255, 255, 0) 15%);
                        background-origin: border-box;
                        background-clip: content-box, border-box;

                        g {
                            display: none;
                        }
                    }
                }
            }

            p {
                font-family: Inter;
                font-size: 12px;
                font-weight: 300;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: left;
                color: #fff;
                margin: 0px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
            }
        }

    }
}

.ViewSection {
    display: flex;
    align-items: center;

    .rowAlignTitle {
        display: flex;
        align-items: center;
        font-family: Inter;
        font-weight: 300;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: 0px;
        gap: 5px;
        text-transform: none;

        svg {
            width: 24px;
            height: 24px;
        }
    }

    .shareVideoIcon {
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        line-height: normal;
        text-align: left;
        color: rgba(255, 255, 255, 0.7);
        display: flex;
        align-items: center;
        margin-left: 16px;
        cursor: pointer;

        svg {
            margin-right: 4px;
        }
    }

}

.rowAlign {
    display: flex;
    align-items: center;
    font-family: Inter;
    font-weight: 300;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 8px;
    gap: 5px;
    text-transform: none;
}

.footerSection {
    display: grid;
    grid: 20% auto 20%;
    grid-template-columns: 20% 60% 20%;
    border-top: 1px solid #000;
    align-items: center;
    padding: 8px 6px 0px 24px;
    margin-top: auto;

    .termsAndPatent {
        position: relative;
        text-align: center;

        .TermsandConditions {
            font-family: Noto Sans;
            font-size: 12px;
            font-weight: 300;
            line-height: 1.6;
            text-align: center;
            color: #fff;
            cursor: pointer;

            &:focus {
                text-decoration: underline;
            }
        }

        .patentPendingText {
            font-family: Noto Sans;
            font-size: 12px;
            font-weight: 300;
            line-height: 1.6;
            text-align: center;
            color: #fff;
        }

        .version {
            color: #70ff00;
        }
    }

    .backBtn {
        font-family: Noto Sans;
        font-size: 18px;
        font-weight: normal;
        line-height: 1.6;
        text-align: center;
        color: #fff;
        border: 0px;
        outline: none;
        background-color: transparent;
        text-align: left;
        cursor: pointer;
        transition: all 0.1s;

        &:hover {
            color: #b3b3b3;
        }
    }
}

.noVideosText {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 54vh;
    color: white;
}