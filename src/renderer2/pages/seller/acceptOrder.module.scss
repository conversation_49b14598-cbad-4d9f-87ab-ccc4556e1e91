.disputeHeader {
  background-color: rgba(0, 0, 0, 0.9);
  color: #fff;
  font-size: 19px;
  padding: 12px 16px;
}

.acceptOrderContent {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .acceptOrderHead {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    background-color: rgba(255, 255, 255, 0.08);
    padding: 12px 20px 12px 24px;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.15;
    letter-spacing: 0.98px;
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    justify-content: space-between;
    .prevOrder {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      &:hover {
        color: #fff;
        svg{
          path{
            stroke-opacity: 1;
          }
        }
      }
    }
    .nextOrder {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      &:hover {
        color: #fff;
        svg{
          path{
            stroke-opacity: 1;
          }
        }
      }
    }
    .nextprevdisabledBtn {
      opacity: 0.5;
      cursor: not-allowed;
      color: rgba(255, 255, 255, 0.5);
      &:hover {
        opacity: 0.5;
        color: rgba(255, 255, 255, 0.5);
        svg{
          path{
            stroke-opacity: 0.5;
          }
        }
      }
    }

    button {
      &:disabled {
        color: rgb(255, 255, 255);
        border: solid 0.5px rgb(255, 255, 255);
        background-color: rgba(0, 0, 0, 0.5);
        opacity: 0.2;
        font-weight: 300;
        cursor: not-allowed;

        &:hover {
          border: solid 0.5px rgb(255, 255, 255);
        }
      }
    }

    .btnPreNextPo {
      font-family: Noto Sans;
      font-size: 16px;
      font-weight: 300;
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.25);
      color: #fff;
      height: 56px;
      width: 56px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 0;

      &:hover,
      &:focus {
        border: solid 1px #70ff00;
      }
    }

    .acceptOrderBtn {
      font-family: Noto Sans;
      font-size: 20px;
      font-weight: 300;
      line-height: 1.2;
      letter-spacing: 4px;
      color: #fff;
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.25);
      width: 100%;
      height: 56px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border: 0;

      &:hover {
        border: solid 1px #70ff00;
        font-weight: 600;
        background-color: #70ff00;
        color: #000;
      }

      &:focus {
        border: solid 1px #70ff00;
      }
    }

    .orderPreviewBtnMain {
      width: auto;
    }

    .orderPreviewBtn {
      border-radius: 4px;
      width: 100%;
      height: 56px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: not-allowed;
      border: 0;
      background-color: rgba(255, 255, 255, 0.75);
      font-family: Noto Sans;
      font-size: 20px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: 4px;
      text-align: center;
      color: #000;
      flex-direction: column;
      position: relative;

      .leftIcon {
        position: absolute;
        left: 8px;
      }

      .rightIcon {
        position: absolute;
        right: 8px;
      }

      .acceptReview {
        font-size: 14px;
        letter-spacing: normal;
      }
    }
    .availableToClaim {
      font-family: Syncopate;
      font-size: 12px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.2;
      letter-spacing: 0.48px;
      text-align: left;
      color: #32ccff;
      text-transform: uppercase;
    }
  }

  .acceptOrderInformation {
    width: 100%;
    padding: 16px;
    background-color: #191a20;

    .acceptOrderInfoTop {
      display: flex;
      width: 100%;
      padding: 16px;
      background-color: rgba(255, 255, 255, 0.04);
      border-radius: 10px;
      font-family: Inter;
      font-size: 14px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: 0.56px;
      text-align: left;
      color: rgba(255, 255, 255, 0.5);

      .acceptOrderInformationCol1 {
        display: flex;
        flex-direction: column;
        row-gap: 23px;
        flex: 1;
      }

      .infoRow {
        display: flex;
        column-gap: 12px;

        .infoLabel {
          flex: 0 165px;
        }

        .infoValue {
          color: #fff;
          display: flex;
          flex-direction: column;
          gap: 8px;
        }
      }

      .acceptOrderInformationCol3 {
        .infoRow {
          .infoLabel {
            flex: 0 107px;
          }

          .infoValue {
            flex: 1;
            color: #fff;

            span {
              font-family: Inter;
              font-size: 12px;
              font-weight: 300;
              font-style: normal;
              line-height: 1.3;
              letter-spacing: 0.48px;
              text-align: left;
              color: rgba(255, 255, 255, 0.6);
            }
          }
        }
      }
    }


  }

  .acceptOrderInformationContainer {
    height: calc(100% - 50px);
    display: flex;
    flex-direction: column;
    background-color: #1e1e24;
    .addPoLineTable {
      flex: 1;
    }
  }


  .addPoLineTable {
    position: relative;
    overflow-y: auto;
    z-index: 1;
    width: 100%;
    box-shadow: 0 -16px 15.1px -11px rgba(0, 0, 0, 0.6);
    // min-height: 635px;

    &::-webkit-scrollbar {
      width: 0px;
      height: 6px;
    }

    thead {
      position: sticky;
      top: 0px;
      z-index: 100;
      border-top: 1px solid rgba(255, 255, 255, 0.2980392157);
      background-color: #1e1e24;
    }

    thead,
    tbody tr {
      display: table;
      width: 100%;
    }

    table {
      width: 100%;
      border-spacing: 1px;
      border-collapse: collapse;

      tr {

        th {
          font-family: Syncopate;
          font-size: 16px;
          font-weight: bold;
          line-height: 1;
          letter-spacing: normal;
          text-align: left;
          color: #9b9eac;
          padding-top: 16px;
          padding-bottom: 16px;

          &:nth-child(1) {
            width: 90px;
            text-align: center;
          }

          &:nth-child(2) {
            width: 270px;
          }

          &:nth-child(3) {
            width: 130px;
            padding-left: 0px;
            text-align: center;
          }

          &:nth-child(4) {
            width: 130px;
            text-align: center;
          }

          &:nth-child(5) {
            width: 140px;
            text-align: right;
            padding-right: 24px;
          }
        }
      }

      tr {

        td {
          vertical-align: top;
          font-family: Inter;
          font-size: 14px;
          font-weight: normal;
          line-height: 1.43;
          letter-spacing: 1.4px;
          text-align: left;
          color: #fff;
          padding-bottom: 32px;

          .rowIndex {
            width: 30px;
            height: 30px;
            object-fit: contain;
            border-radius: 5px;
            border: solid 0.5px rgba(255, 255, 255, 0.16);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-family: Syncopate;
            font-size: 16px;
            font-weight: bold;
            line-height: 1;
            letter-spacing: 1.12px;
            text-align: center;
            color: #9b9eac;
          }

          &:nth-child(1) {
            width: 90px;
          }

          &:nth-child(2) {
            width: 270px;
            text-transform: uppercase;

            .firstLineDesc {
              font-weight: 600;
            }
          }

          &:nth-child(3) {
            width: 130px;
            padding-left: 0px;
            text-align: center;

            .prdQtyUnit {
              position: absolute;
              margin-left: 20px;
            }
          }

          &:nth-child(4) {
            width: 130px;
            text-align: center;

            .prdQtyUnit {
              position: absolute;
              margin-left: 20px;
            }
          }

          &:nth-child(5) {
            width: 140px;
            text-align: right;
              padding-right: 24px;
          }


          .errorQty {
            box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
            background-image: linear-gradient(159deg, #720d16 -254%, #ff4859 98%), linear-gradient(37deg, #8c8b99 168%, #2f2e33 19%);
          }

          .poPerUm {
            font-family: Inter;
            font-size: 14px;
            text-align: center;
            color: #fff;
            display: flex;
            height: 45px;
            align-items: center;
            width: 105px;
            justify-content: center;
          }


          .extendedValue {
            font-family: Inter;
            font-size: 14px;
            text-align: right;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: right;
            height: 45px;
          }

        }
      }
    }
  }



  .acceptOrderBottom {
    display: flex;
    position: absolute;
    bottom: 12px;
    right: 13px;
    left: 13px;
    gap: 8px;

    .returnToSearch {
      button {
        padding: 12px;
        border-radius: 4px;
        border: solid 1px rgba(255, 255, 255, 0.05);
        background-color: rgba(255, 255, 255, 0.1);
        white-space: nowrap;
        font-family: Noto Sans;
        font-size: 12px;
        line-height: 1.2;
        text-align: left;
        color: #fff;
        cursor: pointer;
        width: 85px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:focus {
          border: solid 1px #70ff00;
        }
      }
    }

    .textOfCondition {
      font-family: Noto Sans;
      font-size: 12px;
      font-weight: 300;
      line-height: 1.6;
      text-align: left;
      color: #fff;
      opacity: 0.6;
    }
  }
}

.SubmitApp {
  .dialogContent {
    max-width: 557px;
    width: 100%;
    height: 650px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    object-fit: contain;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: left;
    color: #fff;
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.8);
    background-color: #0f0f14;
    padding: 12px;

    ul {
      background-color: rgba(255, 255, 255, 0.04);
      border-radius: 16px;
      padding: 20px 30px 0px 30px;
      font-family: Inter;
      font-size: 14px;
      font-weight: 300;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: left;
      color: #fff;
      margin-bottom: 40px;
    }

    li {
      margin-bottom: 20px;
      margin-left: 22px;

      &::marker {
        color: rgba(255, 255, 255, 0.4);
        // background-color: rgba(255, 255, 255, 0.4);
      }
    }

    .closeIcon {
      position: absolute;
      top: 10px;
      right: 12px;
      cursor: pointer;

      svg {
        height: 25px;
        width: 25px;
        color: white;

        path {
          fill: #fff
        }
      }
    }

    .disclaimer {
      border-radius: 16px;
      background-color: rgba(255, 255, 255, 0.04);
      font-family: Syncopate;
      font-size: 28px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: -1.12px;
      text-align: center;
      color: #fff;
      height: 109px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;
    }

    .flx {
      display: flex;
      justify-content: right;
      margin-top: 20px;
      gap: 10px;

      .submitBtn {
        width: 309px;
        height: 57px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        border-radius: 12px;
        box-shadow: 0 -0.125rem 0.5rem 0 rgba(0, 0, 0, 0.8);
        background-image: linear-gradient(128deg, #1c40e7 -23%, #16b9ff 116%);
        &:hover{
          background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2)), linear-gradient(128deg, #1c40e7 -23%, #16b9ff 116%);
        }
        &[disabled] {
          cursor: not-allowed;
          background: rgba(255, 255, 255, 0.1);
          &:hover{
            background: rgba(255, 255, 255, 0.1);
          }

          span {
            opacity: 0.3;
          }
        }

        span {
          color: #fff;
          font-family: Syncopate;
          font-size: 1.125rem;
          font-weight: bold;
          font-stretch: normal;
          font-style: normal;
          line-height: 1.3;
          letter-spacing: -0.045rem;
          text-align: center;
      
          text-transform: uppercase;
        }


      }

      .cancelBtn {
        opacity: 0.7;
        transition: all 0.1s;
        border-radius: 10px;
        background-color: rgba(255, 255, 255, 0.1);
        height: 57px;
        width: 212px;
        font-family: Syncopate;
        font-size: 18px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: -0.72px;
        text-align: center;
        color: #fff;

        &:hover {
          opacity: unset;
        }

        &:focus-within {
          opacity: unset;
        }
      }
    }
  }
}

.successPopup {
  .dialogContent {
    height: 479px;

    .successPopupTitle {
      font-family: Noto Sans;
      font-size: 18px;
      font-weight: normal;
      line-height: 1.6;
      text-align: center;
      color: #70ff00;
    }
  }

}

.agreeCheckbox {
  position: relative;
  cursor: pointer;
  text-align: center;

  input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  .lblChk {
    font-family: Inter;
    font-size: 16px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.6);
    margin-left: 36px;
  }

  .checkmark {
    position: absolute;
    border: none;
    height: 22px;
    width: 22px;

    &:focus-within {
      border: 1px solid #fff;
    }
  }
}

// new design style
.acceptOrderContainer {
  height: 100%;
  width: 100%;
  background-color: #191a20;
}
