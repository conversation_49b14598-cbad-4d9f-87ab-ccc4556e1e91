import { create } from 'zustand';

const defaultStore   = {
    selectedIndex: -1,
    selectedObject: null,
    createPoUpdatedData: {},
    keepFocus: false,
    isSearchMode: false,
    searchResult: null,
}

export const useGlobalSearchStore = create((set, get) => ({
    ...defaultStore,
    setIsSearchMode: (v) => set(state => ({ isSearchMode: typeof v === 'function' ? v(state.isSearchMode) : v })),
    setSearchResult: (v) => set(state => ({ searchResult: typeof v === 'function' ? v(state.searchResult) : v })),
    setKeepFocus: (v) => set(state => ({ keepFocus: typeof v === 'function' ? v(state.keepFocus) : v })),
    setCreatePoUpdatedData: (v) => set(state => ({ createPoUpdatedData: typeof v === 'function' ? v(state.createPoUpdatedData) : v })),
    setSelectedIndex: (v) => set(state => ({ selectedIndex: typeof v === 'function' ? v(state.selectedIndex) : v })),
    setSelectedObject: (v) => set(state => ({ selectedObject: typeof v === 'function' ? v(state.selectedObject) : v })),
    resetGlobalSearchStore: () => set({...defaultStore})
}));
