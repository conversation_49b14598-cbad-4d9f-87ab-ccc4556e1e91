import React, { useState, useRef, useEffect } from "react";
import { ClickAwayListener } from "@mui/material";
import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import "./SearchBox.css"; // for styling
import { options as commonOptions, purchaseOrder, routes, userRole } from "src/renderer2/common";
import { useNavigate, useLocation } from "react-router-dom";
import { Widgets } from "@mui/icons-material";
import {
  searchProducts,
  useCreatePoStore,
  useGlobalClockStore,
  useGlobalStore,
  useOrderManagementStore,
  useSearchStore,
  useSellerOrderStore,
  useSubscriptionStore,
} from "@bryzos/giss-ui-library";
import {
  filterObjectsByString,
  flattenObject,
  formatToTwoDecimalPlaces,
  handleOrderManagementNavigation
} from "src/renderer2/helper";
import { useGlobalSearchStore } from "./globalSearchStore";
import { CustomMenu } from "../buyer/CustomMenu";
import SearchResultMenu from "./searchResultMenu";
import styles from "./searchBox.module.scss";
import { ReactComponent as SearchIcon } from "../../assets/New-images/Search.svg";
import { ReactComponent as DropdownIcon } from "../../assets/New-images/New-Image-latest/Polygon.svg";
import { ReactComponent as CheckIcon } from "../../assets/New-images/New-Image-latest/icon-check-dropdown.svg";
import useGetDraftLines from "src/renderer2/hooks/useGetDraftLines";
import moment from "moment";

// 🆕 server search
import usePostGlobalSearch, {
  GlobalSearchResponse,
  GlobalSearchResultItem,
} from "src/renderer2/hooks/usePostGlobalSearch";
import { getFilteredDataByCriteria, getSearchDataForPreviewAndClaimOrders, mergeSearchResults, searchLocalOrders } from "./utils";

type MenuItem = {
  id: string;
  label: string;
  date?: string;
  // extra fields used by onItemClick:
  __category?: "pricing" | "quote" | "purchase" | "order" | "preview_order" | "claim_order";
  __indexWithinCategory?: number;
  __raw?: GlobalSearchResultItem;
};

type Criteria =
  | "all"
  | "instant_pricing"
  | "order_management"
  | "quote"
  | "purchasing"
  | "preview_orders"
  | "claim_orders";

const LABEL_TO_CRITERIA: Record<string, Criteria> = {
  "All": "all",
  "Instant Price Search": "instant_pricing",
  "Order Management": "order_management",
  "Quoting": "quote",
  "Purchasing": "purchasing",

  // Seller options mapping (best-effort)
  "Preview Orders": "preview_orders",
  "Claim Orders": "claim_orders",
};

const CATEGORY_TO_ROUTE: Record<"pricing" | "quote" | "purchase" | "order", string> = {
  pricing: routes.homePage,
  quote: routes.quotePage,
  purchase: routes.createPoPage,
  order: routes.orderManagementPage,
};

const SearchBox: React.FC<{ onSubscribeClick: () => void }> = ({ onSubscribeClick }) => {
  const [isFocused, setIsFocused] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [searchAnchorEl, setSearchAnchorEl] = useState<null | HTMLElement>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const open = Boolean(anchorEl);
  const filteredPoList = useSellerOrderStore((state: any) => state.filteredPoList);
  const [filteredObjects, setFilteredObjects] = useState<any[]>([]);
  const { setSelectedIndex, setSelectedObject, setKeepFocus, keepFocus, setIsSearchMode, setSearchResult } =
    useGlobalSearchStore();
  const { userData, referenceData } = useGlobalStore();
  const [searchText, setSearchText] = useState("");
  const [localResults, setLocalResults] = useState<any[]>([]);
  const { mutate: postGlobalSearch, data: serverData, isLoading:isPending } = usePostGlobalSearch();
  const isSeller = userData?.data?.type === userRole.sellerUser
  const { userSubscription } = useSubscriptionStore();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [showWarmupLoading, setShowWarmupLoading] = useState(false);
  const MIN_LOADER_MS = 200;
  const loaderStartRef = useRef<number | null>(null);

  // 🆕 Options with “All”
  const [options, setOptions] = useState<{ label: string; route?: string | null }[]>([]);
  const buyerOptions = [
    { label: "All", route: null }, // <- does not route
    { label: "Instant Price Search", route: routes.homePage },
    { label: "Quoting", route: routes.quotePage },
    { label: "Purchasing", route: routes.createPoPage },
    { label: "Order Management", route: routes.orderManagementPage },
  ];
  const sellerOptions = [
    { label: "All", route: null }, // <- does not route
    { label: "Preview Orders", route: routes.previewOrderPage },
    { label: "Claim Orders", route: routes.orderPage },
    { label: "Order Management", route: routes.orderManagementPage },
  ];

  const currentPath = location.pathname;
  const [selectedOption, setSelectedOption] = useState<{ label: string; route?: string | null } | null>(null);

  // 🧠 Debounce
  const debounceRef = useRef<number | null>(null);

  useEffect(() => {
    if (!userData || !userData.data) return;
    if (userData?.data?.type === "SELLER") {
      setOptions(sellerOptions);
      setSelectedOption(sellerOptions[0]);
    } else {
      setOptions(buyerOptions);
      setSelectedOption(buyerOptions[0]);
    }
  }, [userData]);

  useEffect(() => {
    if (!selectedOption) return;
    //if (selectedOption.label === "All") return; // “All” behaves like a router OFF (no navigation)
    const newSelectedOption = options.find((option) => currentPath === option.route);
    setSelectedOption(newSelectedOption || options[0]);
  }, [currentPath]);

  useEffect(() => {
    if (keepFocus) {
      searchInputRef.current?.focus();
      setKeepFocus(false);
    }
  }, [location.pathname]);

  const handleDropdownButtonClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
    setIsFocused(true);
  };

  const handleMenuItemClick = (option: { label: string; route?: string | null }) => {
    setSelectedOption(option);
    if (option.route) {
      navigate(option.route);
    }
    // keep focus on search after switching
    setKeepFocus(true);
    searchInputRef.current?.focus();
    setAnchorEl(null);
  };

  const handleClickAway = () => {
    setIsFocused(false);
    setAnchorEl(null);
    setFilteredObjects([]);
    setSearchText("");
  };

  const handleSearchResultMenuClose = () => {
    setFilteredObjects([]);
    setSearchText("");
  };

  // 🆕 Debounced server search on every keystroke (≥ 1 char)
  const triggerServerSearch = (term: string) => {
    
   
    // figure criteria from selected option; fallback to 'all'
    const criteria = (selectedOption && LABEL_TO_CRITERIA[selectedOption.label]) || "all";
     
      let localSearchResults: GlobalSearchResultItem[] = [];

      const shouldSearchLocallyOnly = isSeller && (criteria === "preview_orders" || criteria === "claim_orders");


      if (shouldSearchLocallyOnly) {
        const searchData = getSearchDataForPreviewAndClaimOrders(filteredPoList);
        const filteredData = getFilteredDataByCriteria(searchData, criteria);
        localSearchResults = searchLocalOrders(filteredData, term);

        setLocalResults(localSearchResults);

        const localResponse = mergeSearchResults(null, localSearchResults, term);

        const menuItems = toMenuItems(localResponse);
        setFilteredObjects(menuItems);

          setShowWarmupLoading(false);
  loaderStartRef.current = null;
    hydrateFromServer(localResponse);
    
    return; // Exit early - no server call needed
      }

      if (isSeller && (criteria === "order_management" || criteria === "all")) {
        const searchData = getSearchDataForPreviewAndClaimOrders(filteredPoList);
        const flatLocalData = searchData.flatMap(group => group.data);
        localSearchResults = searchLocalOrders(flatLocalData, term);
      }
      setLocalResults(localSearchResults);

    if (!shouldSearchLocallyOnly) {
        postGlobalSearch({
            data: {
              search_term: term,
              search_criteria: (isSeller && !shouldSearchLocallyOnly) ? 'all' : criteria,
            },
          });
    }
  };

  // Build UI list from server buckets
  const hydrateFromServer = (resp?: GlobalSearchResponse | null) => {
    const mergedResponse = mergeSearchResults(resp || null, localResults, searchText);
    const items = toMenuItems(mergedResponse || null);

    const criteria = (selectedOption && LABEL_TO_CRITERIA[selectedOption.label]) || "all";
    const shouldSearchLocallyOnly = isSeller && (criteria === "preview_orders" || criteria === "claim_orders");
    if (!shouldSearchLocallyOnly) {
        setFilteredObjects(items);
      }
    setSearchResult(mergedResponse);
  };

  useEffect(() => {
    // Only handle local results if we don't have server data yet
    if (serverData || localResults.length === 0) return;
    
    const localSearchResponse = mergeSearchResults(null, localResults, searchText);
    setSearchResult(localSearchResponse);
  }, [localResults, searchText, serverData]);

  // When the server returns, hydrate UI list
  useEffect(() => {
    if (!serverData) return;
    hydrateFromServer(serverData as GlobalSearchResponse | null);
    const started = loaderStartRef.current ?? performance.now();
    const elapsed = performance.now() - started;
    const remaining = Math.max(0, MIN_LOADER_MS - elapsed);
    const t = window.setTimeout(() => {
      setShowWarmupLoading(false);
      loaderStartRef.current = null;
    }, remaining);
    return () => window.clearTimeout(t);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [serverData, localResults]);

  const searchChangeHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
    const val = event.target.value;
    setSearchText(val);


    if (debounceRef.current) {
      window.clearTimeout(debounceRef.current);
      debounceRef.current = null;
    }

    if (val.trim().length === 0) {
      setShowWarmupLoading(false);       // ← stop animation if cleared
      setFilteredObjects([]);
      setSearchResult(null);
      return;
    }

    // start the animated background immediately
    setShowWarmupLoading(true);
    loaderStartRef.current = performance.now();
    if (val.trim().length >= 3) {
      debounceRef.current = window.setTimeout(() => {
        triggerServerSearch(val.trim());   // this will flip to real "isPending"
      }, 300);
    }
  };

  const handleSearchFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    setSearchAnchorEl(event.currentTarget);
  };


  const handleOnItemClick = (obj: any) => {
    setSearchText('');
    setFilteredObjects([]);
    handleOrderManagementNavigation(null, location.pathname, handleSearchSelection, [obj]);
  }

  const handleOnMenuItemClick = (option: any) => {
    setAnchorEl(null);
    handleOrderManagementNavigation(option.route, location.pathname, handleMenuItemClick, [option]);
  }
  // 🆕 Selection behavior: if “All”, decide route by category
  const handleSearchSelection = (item: MenuItem) => {
    setFilteredObjects([]);
    setSearchText("");
    setIsFocused(false);

    const category = item.__category;
    const idx = item.__indexWithinCategory ?? 0;
    const raw = item.__raw!;

    setIsSearchMode(true);

    const goTo = (route: string, payload: GlobalSearchResultItem) => {
      navigate(route);
      //setSelectedIndex(0);
      setSelectedObject({ ...payload });
    };
    return goTo(routes.searchResult, raw);

    // If "All", route by category; otherwise route by the current dropdown
    // const isAll = selectedOption?.label === "All";
    // if (isAll && category) {
    //   switch (category) {
    //     case "pricing": return goTo(routes.searchResult, raw);
    //     case "quote": return goTo(routes.searchResult, raw);
    //     case "purchase": return goTo(routes.searchResult, raw);
    //     case "order": return goTo(routes.searchResult, raw);
    //   }
    // }

    // // Non-"All" paths (same mapping as before)
    // switch (selectedOption?.label) {
    //   case "Instant Price Search": return goTo(routes.homePage, raw);
    //   case "Quoting": return goTo(routes.quotePage, raw);
    //   case "Purchasing": return goTo(routes.createPoPage, raw);
    //   case "order Management": return goTo(routes.orderManagementPage, raw);
    //   case "Preview Orders": return goTo(routes.previewOrderPage, raw);
    //   case "Claim Orders": return goTo(routes.orderPage, raw);
    //   case "Deleted Items": return goTo(routes.deleteOrderPage, raw);
    //   default: return goTo(routes.orderManagementPage, raw);
    // }
  };


  const toMenuItems = (
    resp: GlobalSearchResponse | null | undefined
  ): MenuItem[] => {
    if (!resp?.results) return [];
    const { pricing = [], quote = [], purchase = [], order = [], preview_order = [], claim_order = [] } = resp.results;

    const mk = (
      arr: GlobalSearchResultItem[],
      category: "pricing" | "quote" | "purchase" | "order" | "preview_order" | "claim_order"
    ): MenuItem[] =>
      arr.map((it, idx) => ({
        id: it.id,
        label: it.found_text || it.lines_text || it.address || it.city || it.state_code || "",
        date: it.delivery_date || it.created_date || "",
        __category: category,
        __indexWithinCategory: idx,
        __raw: it,
      }));

    return [
      ...mk(pricing, "pricing"),
      ...mk(quote, "quote"),
      ...mk(purchase, "purchase"),
      ...mk(order, "order"),
      ...mk(preview_order, "preview_order"),
      ...mk(claim_order, "claim_order"),
    ];
  };

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <div className="search-container">
        <div className="search-button-and-field">
          {isFocused ? (
            <>
              <Button className={styles.searchTypeBtn} onClick={handleDropdownButtonClick}>
                {selectedOption?.label} <DropdownIcon className={styles.dropdownIcon} />
              </Button>
              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={() => setAnchorEl(null)}
                PaperProps={{
                  className: styles.dropdownMenu,
                  style: {
                    width: anchorEl?.offsetWidth || 300,
                    backgroundColor: "transparent",
                  },
                }}
              >
                {options.map((option, index) => (
                  <MenuItem
                    key={index}
                    onClick={() => handleOnMenuItemClick(option)}
                    disabled={option.route === undefined /* leave undefineds disabled; null = allowed */}
                  >
                    {selectedOption?.label === option.label && <CheckIcon />}
                    {option.label}
                  </MenuItem>
                ))}
              </Menu>
            </>
          ) : (<>
          {/* {
            ((!userSubscription?.subscription_id || Object.keys(userSubscription).length === 0) && userData?.data?.type !== 'SELLER') && (
              <Button className={styles.subscribeBtn} onClick={onSubscribeClick} variant="contained" disabled={true} data-hover-video-id='subscribe'>
                Subscribe
              </Button>
            )
          } */}
          </>
          )}

          <div className={styles.globalSearchField} data-hover-video-id='global-search'>
            <SearchIcon className={styles.searchIcon} />
            <input
              ref={searchInputRef}
              className={styles.searchInput}
              placeholder="Search Your Account"
              value={searchText}
              onFocus={handleSearchFocus}
              onChange={searchChangeHandler}
              style={isFocused ? { borderRadius: "0px 30px 30px 0px" } : { borderRadius: "30px" }}
            />
            <SearchResultMenu
              anchorEl={searchAnchorEl}
              // keep the menu mounted while user is typing in the field
              open={Boolean(searchAnchorEl) && searchText.trim().length >= 1} // or >= 3
              onClose={handleSearchResultMenuClose}
              items={filteredObjects}
              onItemClick={handleOnItemClick}
              // PaperProps={{
              //   className: styles.dropdownMenu,
              //   style: {
              //     minWidth: searchAnchorEl?.offsetWidth || 300,
              //     backgroundColor: 'transparent',
              //   },
              // }}
              // onItemClick={handleSearchSelection}
              isLoading={isPending}
              showLoadingBackground={showWarmupLoading || isPending}
              showCategoryHeaders={selectedOption?.label === "All"}
              searchTerm={searchText}
            />


          </div>
        </div>
      </div>
    </ClickAwayListener>
  );
};

export default SearchBox;
