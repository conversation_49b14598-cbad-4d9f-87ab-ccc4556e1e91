import { Dialog } from '@mui/material';
import React, { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router';
import styles from './CreatePo.module.scss';
import clsx from 'clsx';
import {  useFieldArray } from 'react-hook-form';
import { useDebouncedValue } from '@mantine/hooks';
import dayjs from 'dayjs';
import useGetUserPartData from '../../../hooks/useGetUserPartData';
import { useQueryClient } from '@tanstack/react-query';
import { v4 as uuidv4 } from 'uuid';
import { useGlobalStore, useSaveUserActivity, getFloatRemainder, getValUsingUnitKey, useBuyerSettingStore, useCreatePoStore, uploadBomConst, getSocketConnection, useBuyerCheckOutNode, orderIncrementPrefix, priceUnits, newPricingPrefix, getChannelWindow, commomKeys} from '@bryzos/giss-ui-library';
import { calculateBuyerTotalOrderWeightForGear } from '../../../utility/pdfUtils';
import SearchHeader from '../../SearchHeader';
import { useRightWindowStore } from '../../RightWindow/RightWindowStore';
import { routes, bomLineStatusCountObjDefault, localStorageKeys, navigationConfirmMessages } from 'src/renderer2/common';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import UploadBomSummary from 'src/renderer2/component/UploadBomSummary/UploadBomSummary';
import BomProcessingWindow from 'src/renderer2/component/BomProcessingWindow/BomProcessingWindow';
import useGetAvailableCreditLimit from 'src/renderer2/hooks/useGetAvailableCreditLimit';
import useSaveBomHeaderDetails from 'src/renderer2/hooks/useSaveBomHeaderDetails';
import usePostDraftPo from 'src/renderer2/hooks/usePostDraftPo';
import { useBomPdfExtractorStore } from '../BomPdfExtractor/BomPdfExtractorStore';
import CreatePoHeaderInfo from '../CreatePoHeaderInfo/CreatePoHeaderInfo';
import { useGenericForm } from 'src/renderer2/hooks/useGenericForm';
import { bomReviewSchema } from 'src/renderer2/models/bomReview.model';
// import { useBomReviewStore } from '../BomReview/BomReviewStore';
import Scroller, { ScrollerRef } from 'src/renderer2/component/Scroller';
import CreatePoTable from './components/CreatePoTable';
import OrderSummary from 'src/renderer2/component/OrderSummary/OrderSummary';
import PdfMakePage from '../../PdfMake/pdfMake';
import { getLocal, setLocal } from 'src/renderer2/helper';
import UploadReviewWindow from 'src/renderer2/component/UploadReviewWindow/UploadReviewWindow';
import ConfirmPoHeaderDetails from './components/ConfirmPoHeaderDetails';

const CreatePosComponent = ({componentType}) => {
    const navigate = useNavigate();
    const location = useLocation();
    const { setShowLoader, setCreatePoSessionId, setBackNavigation, referenceData, productData, productMapping, backNavigation, isCreatePoDirty } = useGlobalStore();
    const { bomProductMappingSocketData, setBomProductMappingSocketData, isCreatePOModule, setIsCreatePOModule, setCreatePoData, createPoData, bomProductMappingDataFromSavedBom, createPoDataFromSavedBom, setCreatePoDataFromSavedBom, setBomProductMappingDataFromSavedBom, setBomDataIdToRefresh, bomSummaryViewFilter, setBomSummaryViewFilter, setUploadBomInitialData, uploadBomInitialData } = useCreatePoStore();
    const setQuoteList = useCreatePoStore(state => state.setQuoteList);
    const quoteList = useCreatePoStore(state => state.quoteList);
    const setSelectedQuote = useCreatePoStore(state => state.setSelectedQuote);
    const selectedQuote = useCreatePoStore(state => state.selectedQuote);

    const channelWindow =  getChannelWindow();
    const { bomData } = useBomPdfExtractorStore();
    const showCommonDialog = useDialogStore((state) => state.showCommonDialog);
    const resetDialogStore = useDialogStore((state) => state.resetDialogStore);
    const { setLoadComponent, setShowVideo, bomProcessingWindowProps, setShowBomProcessing, setBOMLineStatusCountObj,  setScrollToBomLine, props } = useRightWindowStore();

    const [products, setProducts] = useState([]);

    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const { data: userPartData } = useGetUserPartData();
    const mutateSaveBomHeaderDetails = useSaveBomHeaderDetails();
    // const setScrollPosition = useBomReviewStore((state) => state.setScrollPosition);
    // const setViewIndex = useBomReviewStore((state) => state.setViewIndex);
    const scrollerRef = useRef<ScrollerRef>(null);
    
    const [sessionId, setSessionId] = useState('');
    const [searchStringData, setSearchString] = useState('');
    // const getBuyingPreference = ueGetBuyingPreference();
    const { buyerSetting } = useBuyerSettingStore();
    // const saveProductSearch = useSaveProductSearchAnaytic();
    const [openDeliveryToDialog, setOpenDeliveryToDialog] = useState(false);
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const createPoContainerRef = useRef(null);
    const headerContainerRef = useRef<HTMLDivElement>(null);
    // const [isCartValid, setIsCartValid] = useState(true);
    // const [isAllCartDataLoaded, setIsAllCartDataLoaded] = useState(false);
    const [pricingBrackets, setPricingBrackets] = useState([]);
    const [disableBidBuyNow, setDisableBidBuyNow] = useState(false);
    const addPoLineTableRef = useRef(null);
    const [hidePoLineScroll, setHidePoLineScroll] = useState(true);
    const [maxScrollHeight, setMaxScrollHeight] = useState(800);
    const formInputGroupRef = useRef(null);
    const [bomUploadResult, setBomUploadResult] = useState([]);
    const getAvailableCreditLimit = useGetAvailableCreditLimit();
    const [isSavedBom, setIsSavedBom] = useState(location.pathname === routes.savedBom);
    const [cameFromSavedBom, setCameFromSavedBom] = useState(false);
    const [isHeaderDetailsConfirmed, setIsHeaderDetailsConfirmed] = useState(false);
    const [currentBomData, setCurrentBomData] = useState(null);
    const [showConfirmPoHeaderDialog, setShowConfirmPoHeaderDialog] = useState(false);
    // Track the cart items length separately
    const socket = getSocketConnection();
    const HeaderDetailsConfirmedRef = useRef(null);

    const createPoHeaderInfoRef = useRef<any>(null);
    const createPoTableRef = useRef<any>(null);

    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    const { initializePoHeaderForm, watch: poHeaderFormWatch, getHeaderFormData} = createPoHeaderInfoRef.current ?? {};
    const {getInitialData, handleScrollerDrag, disableFormValidation, getCartItems, handleSavePo, getExportPoData, watch: createPoTableWatch, updateLocalStoreQuote} = createPoTableRef.current ?? {}; 
    const [focusJobPoInput, setFocusJobPoInput] = useState(false);
    const [isProgrammaticScroll, setIsProgrammaticScroll] = useState(false);
    const { mutate: logUserActivity } = useSaveUserActivity();

    useEffect(() => {
        setIsSavedBom(location.pathname === routes.savedBom);
        // setShowLoader(true)
        return (() => {
            if(location.pathname !== routes.savedBom){
                handleLeavePageAction();
            }
        })
    }, [])

    // Replace the existing handleLeavePageAction function
    const handleLeavePageAction = () => {
        const { bomDataIdToRefresh } = useCreatePoStore.getState();
        if (bomDataIdToRefresh) {
            socket?.emit('getBomDataById', { bom_upload_id: bomDataIdToRefresh });
        }

        saveBomHeaderDetails();
        setBomUploadResult([]);
        if (bomProcessingWindowProps?.isProcessing !== true) {
            setBomProductMappingSocketData(null);//do this conditionally
            setIsCreatePOModule(true);
            setCreatePoDataFromSavedBom(null);
            setBomProductMappingDataFromSavedBom(null);
        }
        setScrollToBomLine(null)
        setBomDataIdToRefresh(null);
    }
    const isBuyerDeletedItemsPage = location.pathname === routes.buyerDeleteOrderPage;
    

    //*IMP*
    // const disableReviewCompleteButton = true || !checkAtLeastOneApproved || isValidBomForm
// console.log("disableReviewCompleteButton >>>>>>.>> ", disableReviewCompleteButton,  !orderInfoIsFilled, !checkAtLeastOneApproved, isValidBomForm)


    // useEffect(() => {
    //     if(currentBomData?.id && getHeaderFormData){
    //         setProps({...props, bomId: currentBomData.id, getHeaderFormData});
    //     }
    // }, [
    //     currentBomData?.id,
    //     getHeaderFormData
    // ]);

    // useEffect(() => {
    //     const component = <UploadBomSummary />;
    //     setLoadComponent(component);
    // }, []);

    useEffect(() => {
        if(props?.watch && !isBuyerDeletedItemsPage){
            const component = <UploadReviewWindow />;
            setLoadComponent(component);
        }
    }, [props?.watch, isBuyerDeletedItemsPage]);

    useEffect(() => {
        if (bomProcessingWindowProps?.isProcessing === true) {
            setShowVideo(false);
            setShowBomProcessing(true);
        } else {
            setShowVideo(true);
            setShowBomProcessing(false);
        }
        // reset();
        // poHeaderForm.reset();
        // setIsAllCartDataLoaded(false);
        setBomUploadResult([])
        return (() => {
            if (!isBuyerDeletedItemsPage) {
            setLoadComponent(null)
            }
            setShowBomProcessing(false);
            if (bomProcessingWindowProps?.isProcessing === true) {
                setLoadComponent(
                    <div >
                        <BomProcessingWindow
                        // isCompleted={processingCompleted}
                        // gameScoreData={getGameScore}
                        />
                    </div>
                );
            }
            setBackNavigation(-1)
        })
    }, []);

    useEffect(() => {
        // console.log("-------initialData----------", initialData);
        if(initializePoHeaderForm){
            setTimeout(()=>{
                _init();
            }, 1000);
        }
    }, [initializePoHeaderForm]);

    const _init = () => {
        if (productData && referenceData) {
            setProducts(productData)
            setPricingBrackets(referenceData?.ref_weight_price_brackets);
            // initializeCreatePOData();
            const _intialData = getInitialData();
            initializePoHeaderForm(_intialData);
        }
        const sessionId = uuidv4();
        setSessionId(sessionId);
        setCreatePoSessionId(sessionId)
        // if (isCreatePOModule) {
        //     const payload = {
        //         "data": {
        //             "session_id": sessionId
        //         }
        //     }
        //     logUserActivity.mutateAsync({ url: import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload }).catch(err => console.error(err));
        // }
    }
    // useEffect(() => {
    //     if (currentBomData?.items?.length > 0) {
    //         const cartItem = [];
    //         const statusCountObj = { ...bomLineStatusCountObjDefault };
    //         for (let i = 0; i < currentBomData.items.length; i++) {
    //             const productObj = {
    //                 lineStatusIndex: i,
    //                 bom_line_id: currentBomData.items[i].id || i,
    //                 lineStatus: currentBomData.items[i].status,
    //                 originalStatus: currentBomData.items[i].original_line_status || currentBomData.items[i].status,
    //                 confidence: currentBomData.items[i].confidence,
    //                 product_tag: currentBomData.items[i].product_tag,
    //                 description: currentBomData.items[i].description,
    //                 specification: currentBomData.items[i].specification,
    //                 search_string: currentBomData.items[i].search_string,
    //                 matched_products: currentBomData.items[i].matched_products,
    //                 selected_products: currentBomData.items[i].selected_products,
    //                 current_page: currentBomData.items[i].current_page,
    //                 total_pages: currentBomData.items[i].total_pages,
    //                 product_index: currentBomData.items[i].product_index,
    //                 grade: currentBomData.items[i].grade,
    //                 qty: currentBomData.items[i].qty?.replace(/[\$,]/g, '') || currentBomData.items[i].qty,
    //                 qty_unit: currentBomData.items[i].qty_unit?.toLowerCase(),
    //                 length: currentBomData.items[i].length,
    //                 weight_per_quantity: currentBomData.items[i].weight_per_quantity,
    //                 matched_product_count: currentBomData.items[i].matched_product_count,
    //                 last_updated_product: currentBomData.items[i]?.last_updated_product ?? 0,
    //                 domesticMaterialOnly: currentBomData.items[i]?.domestic_material_only || false
    //             }
    //             if (productObj.lineStatus === uploadBomConst.lineItemStatus.approved) {
    //                 statusCountObj[uploadBomConst.lineItemStatus.approved]++;
    //             } else if (productObj.lineStatus === uploadBomConst.lineItemStatus.pending) {
    //                 statusCountObj[uploadBomConst.lineItemStatus.pending]++;
    //             } else if (productObj.lineStatus === uploadBomConst.lineItemStatus.skipped) {
    //                 statusCountObj[uploadBomConst.lineItemStatus.skipped]++;
    //             } else if (productObj.lineStatus === uploadBomConst.lineItemStatus.deleted) {
    //                 statusCountObj[uploadBomConst.lineItemStatus.deleted]++;
    //             }
    //             cartItem[i] = productObj;
    //         }
    //         setBomUploadResult(cartItem);
    //         setBOMLineStatusCountObj(statusCountObj);
    //     }
    // }, [currentBomData])

    useEffect(() => {
        if (location.pathname !== routes.savedBom) {
            if (bomData) {
                setCurrentBomData(bomData);
            } else {
                setCurrentBomData(null);
            }
        }
    }, [bomData])


    useEffect(() => {
        setProducts(productData);
    }, [productData]);




    useEffect(() => {
        const handleScrollOutsideDiv = (event) => {
            setIsCalendarOpen(false)
        };
        createPoContainerRef?.current?.addEventListener('scroll', handleScrollOutsideDiv);
        return () => {
            createPoContainerRef?.current?.removeEventListener('scroll', handleScrollOutsideDiv);
        };
    }, []);



    const scrollPoHeaderToBottom = useCallback(() => {
        const container = createPoContainerRef.current;
        if (container) {
            container.scrollTo({ top: 244, behavior: 'smooth' });
        }
    }, []);


    // useEffect(() => {
    //     if (deboucedSearchString && isCreatePOModule) {
    //         handleCreatePOSearch(searchStringData, null, lineSessionId)
    //     }
    // }, [deboucedSearchString])

    const updateQuoteHeaderDetails = () => {
        if(updateLocalStoreQuote?.()){
            updateLocalStoreQuote();
        }
    }

    const saveBomHeaderDetails = () => {
        console.log("saveBomHeaderDetails @>>>>>>>", getHeaderFormData?.());
        updateQuoteHeaderDetails();
        if (location.pathname === routes.bomUploadReview || location.pathname === routes.savedBom) {
            if (currentBomData?.id) {
                setBomDataIdToRefresh(currentBomData?.id);
            }
            try {
                const headerFormData = getHeaderFormData();
                const formattedHeaderFormData = {
                    "bom_name": headerFormData.buyer_internal_po,
                    "bom_type": headerFormData.order_type,
                    "delivery_date": headerFormData.delivery_date,
                    ...headerFormData
                }
                
                const payload = {
                    "data": {
                        "bom_upload_id": currentBomData?.id ?? '',
                        ...formattedHeaderFormData
                    }
                }
                if (currentBomData?.id && payload.data.bom_name && payload.data.bom_type && payload.data.delivery_date && payload.data.shipping_details.line1 && payload.data.shipping_details.city && payload.data.shipping_details.zip && payload.data.shipping_details.state_id) {
                    saveModifiedBomHeader();
                    mutateSaveBomHeaderDetails.mutateAsync(payload)
                    let _updatedBomInitialData = {
                        ...uploadBomInitialData,
                        ...headerFormData
                    }
                    setUploadBomInitialData(_updatedBomInitialData)
                }
            } catch (error) {
                console.error(error)
            }
        }
    }

    const saveModifiedBomHeader = () => {
        try {
            if (currentBomData?.id) {
                const data = {
                    [currentBomData?.id]: "HEADER"
                }
                const _lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
                if (_lastModifiedBom) {
                    const _lastModifiedBomData = JSON.parse(_lastModifiedBom);
                    _lastModifiedBomData[currentBomData?.id] = "HEADER";
                    localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(_lastModifiedBomData));
                } else {
                    localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(data));
                }
            }
        } catch (e) {
            console.warn('Could not store value in localStorage', e);
        }
    }

    // Validates that cart items have required fields if they have a description
    const validateCart = () => {
        const cartItems = getValues('cart_items');
        if (!cartItems?.length) return true;

        // Only validate items that have a description
        const itemsWithDescription = cartItems.filter(item => item?.descriptionObj?.UI_Description && item?.lineStatus !== "SKIPPED" || item?.lineStatus === "APPROVED");
        if (!itemsWithDescription.length) return false;

        // Check required fields are present and valid
        return itemsWithDescription.every(item => {
            const quantity = Number(item.qty);
            return quantity > 0 &&
                Boolean(item.qty_unit) &&
                Boolean(item.price_unit);
        });
    };

    // Monitor cart changes and update validation state
    // useEffect(() => {
    //     const subscription = watch((_, { name }) => {
    //         if (name?.startsWith('cart_items')) {
    //             setIsCartValid(validateCart());
    //         }
    //     });
    //     return () => subscription.unsubscribe();
    // }, [watch]);

    

    const scrollToTop = useCallback(() => {
        if (scrollerRef.current) {
            scrollerRef.current.updateScrollPosition(0);
        }
        const container = createPoContainerRef.current;
        if (container) {
            container.scrollTo({ top: 0, behavior: 'smooth' });
        }
    }, []);

    const handleMainScroll = () => {
        // Skip if the scroll is from dragging the thumb
        // if (isDraggingScrollState) return;

        if (!createPoContainerRef.current) return;

        const { scrollTop, scrollHeight, clientHeight } = createPoContainerRef.current;
        const isAtBottom = Math.floor(Math.abs(scrollHeight - scrollTop - clientHeight)) <= 5; // 5px threshold
        // if (!isDraggingScrollState) setScrollPosition(scrollTop);
        if(scrollerRef.current && !isAtBottom && !isProgrammaticScroll) scrollerRef.current.updateScrollPosition(scrollTop);
        const header = headerContainerRef.current;
        // Handle header opacity
        if (header) {
            let opacity = 0;
            if (scrollTop > 52) {
                opacity = Math.min(scrollTop / 152, 1);
            }
            (header as HTMLElement).style.opacity = opacity.toString();
        }
        // Enable/disable the table scroll based on main scroll position
        if (addPoLineTableRef.current) {
            setHidePoLineScroll(!isAtBottom);
        }
    }

    // Replace the handleNavigateAway function
    const handleNavigateAway = (targetRoute) => {
        const { bomDataIdToRefresh } = useCreatePoStore.getState();
        if (bomDataIdToRefresh) {
            socket?.emit('getBomDataById', { bom_upload_id: bomDataIdToRefresh });
        }
        if (location.pathname === routes.savedBom) {
            setCreatePoDataFromSavedBom(null);
            setBomProductMappingDataFromSavedBom(null);
            setBomProductMappingSocketData(null);
        } else {
            const isDirty = isCreatePoDirty || !isCreatePOModule;
            const message = isCreatePoDirty
                ? navigationConfirmMessages.unsavedChanges
                :
                navigationConfirmMessages.confirmLeave;
            navigateWithConfirmation(isDirty, targetRoute, undefined, message);
        }
        setBomDataIdToRefresh(null);
    };
    
    const navigateWithConfirmation = (shouldConfirm: boolean = false, to: string, options?: any, message?: string ) => {
        const targetRoute = to ? to : location.pathname;
      if (shouldConfirm) {
        handleConfirmNavigation(targetRoute, options, message);
        return;
      }
      // If no confirmation needed, just navigate
      navigate(targetRoute, options);
    };
    // Add the navigation interception
    const handleConfirmNavigation = (to, options, message) => {
        showCommonDialog(
            null, 
            message, 
            null, 
            resetDialogStore, 
            [
                {
                    name: 'Yes', 
                    action: () => handleDialogYesBtn(to, options)
                }, 
                {
                    name: 'No', 
                    action: resetDialogStore
                }
            ]
        );
    };

    const handleDialogYesBtn = (to, options) => {
        handleLeavePageAction();
        setIsCreatePoDirty(false)
        navigate(to, options);
        resetDialogStore();
    }
    // useEffect(() => {
    //     if(!hidePoLineScroll){
    //         setTimeout(() => {
    //             if(scrollerRef.current) scrollerRef.current.updateScrollPosition((addPoLineTableRef.current?.scrollTop??0) + 204);
    //         }, 400);
    //     }
    // }, [hidePoLineScroll])

    
    const saveCreatePOUserActivity = (sessionId: string, checkOutPoNumber: string | undefined, checkOutError: string | undefined) => {
        const payload = {
            "data": {
                "session_id": sessionId,
                "po_number": checkOutPoNumber ?? null,
                "response": checkOutError ?? null,
                "internal_po_number": poHeaderFormWatch("buyer_internal_po") ? poHeaderFormWatch("buyer_internal_po") : null,
                "shipping_details": poHeaderFormWatch("shipping_details") ? poHeaderFormWatch("shipping_details") : null,
                "delivery_date": poHeaderFormWatch("delivery_date") ? poHeaderFormWatch("delivery_date") : null,
                "payment_method": createPoTableWatch("payment_method") ? createPoTableWatch("payment_method") : null,
            }
        }
        logUserActivity({ url: `${import.meta.env.VITE_API_SERVICE}/user/create_po_open_close`, payload });
    }
    const saveUserActivity = (checkOutPoNumber, checkOutError) => {
        if(isCreatePOModule){
            saveCreatePOUserActivity(sessionId, checkOutPoNumber, checkOutError);
        }
    }

    const handleShowConfirmPoHeader = () => {
        // setShowConfirmPoHeaderDialog(true);
        setIsHeaderDetailsConfirmed(false);
    }

    const shouldDisableHeaderInputs = isBuyerDeletedItemsPage;


    return (
        <div className={clsx(styles.createPoContent, 'bgBlurContent')}>
                  
            <div className={styles.formInnerContent} ref={HeaderDetailsConfirmedRef}>
                {/* <div className={styles.headerNoteCreatePO}>
                            <span className={styles.leftIcon}><WarningIcon /></span>

                            <span className={styles.headerNoteText}>All delivered material will be new (aka "prime"), fulfilled to the defined specification,
                                loaded/packaged for forklift and/or magnetic offloading and have mill test reports.</span>
                            <span className={clsx(styles.headerNoteText, styles.marginTop8)}>The maximum bundle weight is 5,000 pounds.</span>
                            <span className={styles.rightIcon}><WarningIcon /></span>
                        </div> */}
                <div className={clsx(styles.tblWrapper, 'w100')}>
                        <Scroller
                            ref={scrollerRef}
                            containerHeight={HeaderDetailsConfirmedRef.current?.clientHeight || 800}
                            contentHeight={maxScrollHeight}
                            onScrollChange={handleScrollerDrag}
                            rightOffset={5}
                            bottomOffset={20}
                            topOffset={20}
                            fixedThumbHeight={100}
                            />
                            <>
                                {/* <div className={clsx(styles.headerContainer)} ref={headerContainerRef} onClick={scrollToTop} >
                                    <div className={styles.headerItem}>
                                        {poHeaderFormWatch?.('internal_po_number')?.toUpperCase() || '-'}
                                    </div>
                                    <div className={styles.headerItem}>
                                        {poHeaderFormWatch?.('delivery_date') ?
                                            `${dayjs(poHeaderFormWatch?.('delivery_date')).format('ddd').toUpperCase()}, ${poHeaderFormWatch?.('delivery_date')}`
                                            : '-'
                                        }
                                    </div>
                                    <div className={styles.headerItem}>
                                        {poHeaderFormWatch?.('order_type')?.toUpperCase() || '-'}
                                    </div>
                                    <div className={styles.headerItem}>
                                        {<><span>{(poHeaderFormWatch?.('shipping_details.line1')?.toUpperCase() || '-')}</span> <span>{(poHeaderFormWatch?.('shipping_details.line2')?.toUpperCase() || '')}</span></>}
                                    </div>
                                </div> */}
                     <div className={clsx(styles.createPOContainer, styles.removeFooter)} ref={createPoContainerRef} onScroll={handleMainScroll}>
                                    <CreatePoHeaderInfo
                                        styles={styles}
                                        ref={createPoHeaderInfoRef}
                                        formInputGroupRef={formInputGroupRef}
                                        isCalendarOpen={isCalendarOpen}
                                        setIsCalendarOpen={setIsCalendarOpen}
                                        setOpenErrorDialog={setOpenErrorDialog}
                                        setErrorMessage={setErrorMessage}
                                        saveBomHeaderDetails={saveBomHeaderDetails}
                                        disableBidBuyNow={disableBidBuyNow}
                                        setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                        openDeliveryToDialog={openDeliveryToDialog}
                                        scrollToTop={scrollToTop}
                                        isSavedBom={isSavedBom}
                                        focusJobPoInput={focusJobPoInput}
                                        setCameFromSavedBom={setCameFromSavedBom}
                                        saveUserActivity={saveUserActivity}
                                        HeaderDetailsConfirmedRef={HeaderDetailsConfirmedRef}
                                        isHeaderDetailsConfirmed={isHeaderDetailsConfirmed}
                                        setIsHeaderDetailsConfirmed={setIsHeaderDetailsConfirmed}
                                        cameFromSavedBom={cameFromSavedBom}
                                        disabled={shouldDisableHeaderInputs}
                                        componentType={componentType}
                                    />
                                    <CreatePoTable 
                                        ref={createPoTableRef}
                                        styles={styles}
                                        createPoContainerRef={createPoContainerRef}
                                        formInputGroupRef={formInputGroupRef}
                                        hidePoLineScroll={hidePoLineScroll}
                                        setHidePoLineScroll={setHidePoLineScroll}
                                        addPoLineTableRef={addPoLineTableRef}
                                        bomUploadResult={bomUploadResult}
                                        products={products}
                                        userPartData={userPartData}
                                        sessionId={sessionId}
                                        searchStringData={searchStringData}
                                        setSearchString={setSearchString}
                                        setDisableBidBuyNow={setDisableBidBuyNow}
                                        setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                        scrollToTop={scrollToTop}
                                        currentBomData={currentBomData}
                                        scrollPoHeaderToBottom={scrollPoHeaderToBottom}
                                        setFocusJobPoInput={setFocusJobPoInput}
                                        scrollerRef={scrollerRef}
                                        setMaxScrollHeight={setMaxScrollHeight}
                                        setCurrentBomData={setCurrentBomData}
                                        isHeaderDetailsConfirmed={isHeaderDetailsConfirmed}
                                        initializePoHeaderForm={initializePoHeaderForm}
                                        setOpenErrorDialog={setOpenErrorDialog}
                                        setErrorMessage={setErrorMessage}
                                        setCameFromSavedBom={setCameFromSavedBom}
                                        maxScrollHeight={maxScrollHeight}
                                        isProgrammaticScroll={isProgrammaticScroll}
                                        setIsProgrammaticScroll={setIsProgrammaticScroll}
                                        cameFromSavedBom={cameFromSavedBom}
                                        pricingBrackets={pricingBrackets}
                                        isCreatePOModule={isCreatePOModule}
                                        poHeaderFormWatch={poHeaderFormWatch}
                                        setBomUploadResult={setBomUploadResult}
                                        navigateWithConfirmation={navigateWithConfirmation}
                                        saveUserActivity={saveUserActivity}
                                        setCreatePoSessionId={setCreatePoSessionId}
                                        componentType={componentType}
                                    />
                                </div>
                            </>
                    {/* {isCreatePOModule && <div className={styles.backBtnMain}>
                        <button className={styles.cancelPOGoBack} onClick={() => handleNavigateAway(backNavigation)} >CANCEL</button>
                        {(channelWindow?.fetchPdf || channelWindow?.generatePdf) &&
                            <PdfMakePage getExportPoData={getExportPoData} buyingPreferenceData={buyerSetting} disabled={disableFormValidation && location.pathname !== routes.savedBom} getCartItems={getCartItems} />
                        }
                        {!isSavedBom && <button className={styles.savePOGoBack} onClick={() => {
                            showCommonDialog(null, 'This will exit Create PO. You can resume from your saved BOM. Do you want to continue?', null, resetDialogStore, [{ name: commomKeys.yes, action: handleSavePo }, { name: commomKeys.no, action: resetDialogStore }]);
                        }} disabled={disableFormValidation} >SAVE PO / BUY LATER</button>}
                    </div>} */}
                </div>
                <Dialog
                    open={openErrorDialog}
                    onClose={(event) => setOpenErrorDialog(false)}
                    transitionDuration={200}
                    hideBackdrop
                    disableScrollLock={true}
                    container={HeaderDetailsConfirmedRef.current}
                    classes={{
                        root: styles.ErrorDialog,
                        paper: styles.dialogContent
                    }}

                >
                    <p>{errorMessage}</p>
                    <button className={styles.submitBtn} onClick={(event) => { setOpenErrorDialog(false); }}>Ok</button>
                </Dialog>

                {/* <Dialog
                    open={!isHeaderDetailsConfirmed && cameFromSavedBom}
                    transitionDuration={200}
                    disableScrollLock={true}
                    container={HeaderDetailsConfirmedRef.current}
                    style={{
                        position: 'absolute',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'rgba(255, 255, 255, 0.1)',
                        border: '1px solid transparent',
                        borderRadius: '0px 0px 20px 20px',
                    }}
                    PaperProps={{
                        style: {
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            margin: 0
                        }
                    }}
                    hideBackdrop
                    classes={{
                        root: styles.confirmHeaderDetailsPopup,
                        paper: styles.dialogContent
                    }}

                >

                    <div className={styles.confirmHeaderDetailsContainer}>
                        <span>CONFIRM HEADER DETAILS</span>
                        <button onClick={() => {
                            setIsHeaderDetailsConfirmed(true);
                        }}>PROCEED</button>
                    </div>


                </Dialog> */}

             

            </div>
        </div>
    )
}


const CreatePo = ({componentType = ""}) => {
    const location = useLocation(); // Add this line to get location

    return (
        <CreatePosComponent componentType={componentType} key={location.pathname}/>
    )
}

export default CreatePo