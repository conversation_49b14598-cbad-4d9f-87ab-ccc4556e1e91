
import React, { useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { buildNotificationSchema } from '../schemas/notificationsSchema';
import styles from './NotificationsTab.module.scss'; // <- use the same stylesheet
import clsx from 'clsx';
import { Fade, Tooltip } from '@mui/material';
import { useBuyerSettingStore, useGlobalStore, usePostSaveUserNotificationSetting } from '@bryzos/giss-ui-library';
import { navigatePage } from 'src/renderer2/helper';
import { routes } from 'src/renderer2/common';


const NotificationsTab: React.FC<any> = ({
  setActiveTab,
  setSaveFunctions,
  setIsFadeLoaderOpen
}: any) => {
  const currentUserType = 'buyer';
  const { referenceNotificationSettings , userNotificationData} = useGlobalStore()
  const schema = buildNotificationSchema(referenceNotificationSettings);
  const {setShowLoader} = useGlobalStore()
  const { mutateAsync: saveUserNotification } = usePostSaveUserNotificationSetting();

  const methods = useForm<{ data: any }>({
    resolver: yupResolver(schema),
    defaultValues: { data: {} },
    mode: 'onBlur',
  });

  const { setValue, handleSubmit, register, watch, reset, formState: { errors , isSubmitting , isDirty , isValid } } = methods;

  const isButtonDisabled = !isValid || isSubmitting || !isDirty;

    useEffect(() => {
      setSaveFunctions({
          onSave: () => handleSubmit(handleSave)(),
          onSaveAndNext: () => handleSubmit(handleSaveAndNext)(),
          onSaveAndExit: () => handleSubmit(handleSaveAndExit)(),
          isDisabled: isButtonDisabled,
      });
  }, [isButtonDisabled, handleSubmit]);

  useEffect(() => {   
    setTimeout(() => {
        const defaultFocusSpan = document.getElementById('defaultFocusSpan');
        if (defaultFocusSpan) {
            defaultFocusSpan.focus();
        }
    }, 100)
  }, []);


  useEffect(() => {
    if (referenceNotificationSettings && userNotificationData) {
      console.log("referenceNotificationSettings", referenceNotificationSettings)
      console.log("userNotificationData", userNotificationData)
      Object.values(referenceNotificationSettings).forEach((events) => {
        events.forEach((event) => {
          const allowed =
            !event.to_user_type ||
            event.to_user_type === currentUserType ||
            event.to_user_type === 'user';

          if (!allowed || !event.reference_column) return;

          const saved = userNotificationData[event.reference_column] || {};

          setValue(`data.${event.reference_column}`, {
            email:  saved.email ?? false,
            text: saved.text ?? false,
            pusher: saved.pusher ?? false,
          });
        });
      });
    }
  }, [referenceNotificationSettings, userNotificationData, currentUserType, setValue]);

  const handleSave = async (data: any) => {
    setIsFadeLoaderOpen(true)
    try{
      const cleanedPayload = {
        data: Object.fromEntries(
          Object.entries(data.data).filter(([key]) => key !== 'null' && key)
        ),
      };
      await saveUserNotification(cleanedPayload)
      reset(data); 
    }catch(error){
      console.error("error", error)
    }finally{
      setIsFadeLoaderOpen(false)
    }
  };

  const handleSaveAndNext = async (data: { data: any }) => {
    await handleSave(data)
    setActiveTab('SUBSCRIPTION')
  }

  const handleSaveAndExit = async (data: { data: any }) => {
    await handleSave(data)
    navigatePage(location.pathname, { path: routes.homePage })
  }

  return (
    <FormProvider {...methods}>
      <div className={styles.notificationMainContainer} data-hover-video-id="settings-notification">
        <div className={styles.notificationHeader}>
          Some notifications are required and unable to opt-out. Select the notifications you would
          like to receive and how you would like receive them: by
          <span className={styles.methodText}> Text <span className={clsx(styles.methodIcon, styles.textIcon)}>T</span>&nbsp;</span>,
          <span className={styles.methodText}> Email <span className={clsx(styles.methodIcon, styles.emailIcon)}>E</span>&nbsp;</span>, or
          <span className={styles.methodText}> Desktop <span className={clsx(styles.methodIcon, styles.desktopIcon)}>D</span>&nbsp;</span>.
        </div>
        <span tabIndex={0} id="defaultFocusSpan"></span>
        <div className={styles.notificationContainer}>
          {/* <div className={styles.notificationSection}>
            <div className={styles.notificationSectionTitle}>MOBILE NUMBER</div>
            <div className={styles.notificationSectionContent}>
              <div className={styles.notificationItem}>
                <span>(*************</span>
                <div className={styles.notificationToggle}>
                  <button>CHANGE NUMBER</button>
                </div>
              </div>
            </div>
          </div> */}
          {(referenceNotificationSettings && userNotificationData) && (
            <>
              {(() => {
                // Define the custom order for categories
                const categoryOrder = [
                  { key: "USER ACCOUNT", displayName: 'USER ACCOUNT' },
                  { key: "ORDER UPDATES", displayName: 'ORDER UPDATES' },
                  { key: "APP FEATURES", displayName: 'APP FEATURES' },
                ];

                return categoryOrder.map(({ key, displayName } , categoryIndex) => {
                  const events = referenceNotificationSettings[key];
                  if (!events || events.length === 0) return null;

                  return (
                    <div key={key} className={styles.notificationSection}>
                      <div className={styles.notificationSectionTitle}>{displayName}</div>
                      <div className={styles.notificationSectionContent}>
                        {events.map((event , index) => {
                          const allowed =
                            !event.to_user_type ||
                            event.to_user_type === currentUserType ||
                            event.to_user_type === 'user';

                          if (!allowed || !event.reference_column) return null;

                          const value = watch(`data.${event.reference_column}`) || {};

                          return (
                            <div key={event.reference_column} className={styles.notificationItem}>
                              <Tooltip
                                title={event.tooltip}
                                placement={"top"}
                                disableInteractive
                                TransitionComponent={Fade}
                                TransitionProps={{ timeout: 100 }}
                                classes={{
                                  tooltip: "inputQtyTooltip",
                                }}
                              >
                                <span>{event.display_title}</span>
                              </Tooltip>
                              {/* {event.tooltip && (
                                <div className={styles.notificationTooltip}>{event.tooltip}</div>
                              )} */}
                              <div className={styles.notificationToggle}>
                                
                              <button 
                                  className={clsx(styles.notificationCheckbox, styles.textType, value.text && styles.checked)}
                                  onClick={() => !true && setValue(`data.${event.reference_column}.text`, !value.text , {
                                    shouldDirty: true,
                                  })}
                                  style={{ cursor: true ? 'not-allowed' : 'pointer' }}
                                  disabled={true}
                                >
                                  T
                                </button>
                                <button 
                                  className={clsx(styles.notificationCheckbox, styles.emailType, value.email && styles.checked)}
                                  onClick={() => Boolean(event.is_email_enabled) && setValue(`data.${event.reference_column}.email`, !value.email , {
                                    shouldDirty: true,
                                  })}
                                  style={{ cursor: !Boolean(event.is_email_enabled) ? 'not-allowed' : 'pointer' }}
                                  disabled={!Boolean(event.is_email_enabled)}
                                >
                                  E
                                </button>

                                <button 
                                  className={clsx(styles.notificationCheckbox, styles.desktopType, value.pusher && styles.checked)}
                                  onClick={() => Boolean(event.is_pusher_enabled) && setValue(`data.${event.reference_column}.pusher`, !value.pusher , {
                                    shouldDirty: true,
                                  })}
                                  style={{ cursor: !Boolean(event.is_pusher_enabled) ? 'not-allowed' : 'pointer' }}
                                  disabled={!Boolean(event.is_pusher_enabled)}
                                  data-last-focusable={categoryIndex === categoryOrder.length - 1 && index === events.length - 1 ? "notifications-tab" : undefined}
                                  onKeyDown={(e) => {
                                    if (e.key === 'Tab' && categoryIndex === categoryOrder.length - 1 && index === events.length - 1) {
                                      if(!e.shiftKey){
                                        e.preventDefault();
                                      const saveButton = document.getElementById('settings-save-button') as HTMLButtonElement;
                                      if (saveButton) {
                                        if (saveButton.disabled) {
                                          const companyButton = document.getElementById('COMPANY')
                                          if (companyButton) {
                                            (companyButton as HTMLElement).focus();
                                          }
                                        } else {
                                          setTimeout(() => {
                                            saveButton.focus();
                                          }, 0);
                                        }
                                      }
                                    }
                                    }
                                  }} 
                                >
                                  D
                                </button>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                });
              })()}
            </>
          )}
        </div>
      </div>
    </FormProvider>
  );
};

export default NotificationsTab;
