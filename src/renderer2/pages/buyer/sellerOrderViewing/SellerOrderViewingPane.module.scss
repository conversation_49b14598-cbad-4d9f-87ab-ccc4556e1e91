.viewingPane {
  width: 100%;
  height: 100%;
  border-style: solid;
  border-width: 1px;
  border-image-source: linear-gradient(to left, #fff -5%, #1b1b21 1%);
  border-image-slice: 1;
  background-color: #191a20;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  
}

.placeholderContent {
  text-align: center;
  z-index: 1;
  position: relative;
  padding: 20px;
  
  // Optional: Add a subtle animation for the placeholder
  animation: fadeInUp 0.6s ease-out;
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  letter-spacing: 1.12px;
  text-align: center;
  color: #fff;
}

.justMissedPopup {
  .dialogContent {
    width: 100%;
    max-width: 555px;
    padding: 56px 37px 56px 37px;
    border-radius: 50px;
    background-color: #0f0f14;
    box-shadow: none;

    .closeIcon{
      position: absolute;
      top: 24px;
      right: 24px;
      cursor: pointer;
    }

    .youJustMissetext {
      font-family: Syncopate;
      font-size: 24px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.96px;
      text-align: center;
      color: #fff;
      margin-bottom: 24px;
    }

    .thisOrderMissedtest {
      font-family: Inter;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: #dbdcde;
      margin-bottom: 8px;
      display: block;
      &.thisOrderMissedtest1{
        margin: 4px 0px 12px 0px;
      }
    }

    .claimAnotherOrderbtn {
      margin-top: 32px;
      height: 60px;
      padding: 0px 17px;
      border-radius: 12px;
      background-image: linear-gradient(119deg, #1c40e7 -12%, #16b9ff 109%);
      font-family: Syncopate;
      font-size: 20px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.8px;
      text-align: center;
      color: #fff;
      text-transform: uppercase;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}



.ErrorDialog {
  .dialogContent {
    max-width: 333px;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 30px 34px 30px 34px;
    object-fit: contain;
    border-radius: 10px;
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.8);
    background-color: rgba(0, 0, 0, 0.72);
    font-family: Noto Sans;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.6;
    text-align: center;
    color: #fff;

    .closeIcon {
      position: absolute;
      top: 10px;
      right: 12px;
      cursor: pointer;
      opacity: 0.5;

      &:hover {
        opacity: unset;
      }

      svg {
        height: 20px;
        width: 20px;
        color: white;

        path {
          fill: #fff
        }
      }
    }

    .youJustMissetext {
      height: 32px;
      align-self: stretch;
      flex-grow: 0;
      font-family: Noto Sans;
      font-size: 20px;
      font-weight: 600;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.6;
      letter-spacing: 0.5px;
      text-align: center;
      color: #fff;
      margin-bottom: 18px;
    }

    .thisOrderMissedtest {
      height: 60px;
      align-self: stretch;
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.4;
      letter-spacing: normal;
      text-align: center;
      color: #fff;

    }

    .missedtest {
      height: 60px;
      align-self: stretch;
      font-family: Noto Sans;
      font-size: 14px;
      line-height: 1.6;
      letter-spacing: normal;
      text-align: left;
      color: #fff;
    }

    .claimAnotherOrderbtn {
      height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background-color: transparent;
      font-family: Noto Sans;
      font-size: 16px;
      font-weight: normal;
      line-height: 1.6;
      text-align: center;
      color: #fff;
      margin-top: 20px;
      transition: all 0.1s;
      border: none;

      &:hover {
        color: #70ff00;
      }

    }

    .submitBtn {
      height: 40px;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 10px 24px;
      border-radius: 4px;
      border: solid 0.5px #fff;
      background-color: transparent;
      font-family: Noto Sans;
      font-size: 14px;
      font-weight: normal;
      line-height: 1.4;
      text-align: center;
      color: #fff;
      margin-top: 20px;
      transition: all 0.1s;

      &:hover {
        background-color: #70ff00;
        border: solid 0.5px #70ff00;
        color: #000;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          border: solid 0.5px #fff;
          background-color: transparent;
          color: #fff;
        }
      }
    }


  }

}
