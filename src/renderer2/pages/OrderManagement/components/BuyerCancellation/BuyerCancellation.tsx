import React, { useState } from 'react';
import styles from '../SellerHeaderInfo/SellerHeaderInfo.module.scss'
import { useSearchStore } from 'src/renderer2/store/SearchStore';

const BuyerCancellation = () => {
    const [showAcceptCancellation, setShowAcceptCancellation] = useState(false);
    const [restockingFee, setRestockingFee] = useState('');
    const setDynamicHeaderComponent = useSearchStore(state => state.setDynamicHeaderComponent);


    const handleAcceptCancellation = () => {
        setShowAcceptCancellation(true);
    };

    const handleRejectCancellation = () => {
        // Go back to previous screen
        setShowAcceptCancellation(false);
        setDynamicHeaderComponent(null);
    };

    const handleSkip = () => {
        // Go back to previous screen
        setShowAcceptCancellation(false);
    };

    const handleSubmit = () => {
        console.log('Restocking Fee Value:', restockingFee);
    };

    return (
        <div className={styles.buyerCancellationContainer}>
            <h2>Buyer Requested To Cancel This Order</h2>
            <p className={styles.buyerCancellationDescription}>If you choose to accept this order cancellation, you'll have the option to apply a restocking fee.</p>

            {!showAcceptCancellation ? (
                <div className={styles.buyerCancellationButtons}>
                    <button onClick={handleAcceptCancellation}>Accept Cancellation</button>
                    <button onClick={handleRejectCancellation}>Reject Cancellation</button>
                </div>
            ) : (
                <div className={styles.acceptCancellationContainer}>
                    <div className={styles.acceptCancellationInput}>
                        <label>Restocking Fee $</label>
                        <input
                            type="text"
                            value={restockingFee}
                            onChange={(e) => setRestockingFee(e.target.value)}
                            placeholder="200.00"
                        />
                    </div>
                    <div className={styles.buyerCancellationButtons}>
                        <button onClick={handleSkip}>Skip</button>
                        <button onClick={handleSubmit}>Submit</button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default BuyerCancellation;