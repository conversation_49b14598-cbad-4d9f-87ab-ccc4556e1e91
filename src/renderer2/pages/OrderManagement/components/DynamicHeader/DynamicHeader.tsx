import React from 'react';
import styles from '../SellerHeaderInfo/SellerHeaderInfo.module.scss'

interface DynamicHeaderProps {
  /** The React component to render dynamically */
  component?: React.ComponentType<any> | null;
  /** Props to pass to the component */
  componentProps?: Record<string, any>;
}

const DynamicHeader: React.FC<DynamicHeaderProps> = ({ 
  component: Component, 
  componentProps = {} 
}) => {
  if (!Component) {
    return null;
  }

  // Use React.createElement to avoid potential hook issues
  return (
    <div className={styles.headerInfoContainer}>
      {React.createElement(Component, componentProps)}
    </div>
  );
};

export default DynamicHeader;
