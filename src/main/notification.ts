// @ts-nocheck
import { nativeTheme, Notification } from 'electron';
import path from 'path';
import { browserWindow } from '.';

let mainWindow;
let notification;
let userId = null;
let store;
let notificationListOfUsers={};
let channelWindow;

export function setNotificationConstants(_userId, _store, _channelWindow, _mainWindow){
    userId = _userId;
    store = _store;
    channelWindow = _channelWindow;
    mainWindow = _mainWindow;
    if(store.has('notificationListOfUsers')){
        notificationListOfUsers = store.get('notificationListOfUsers');
    }
    if( !notificationListOfUsers[userId] ) notificationListOfUsers[userId] = [];
    console.log("Users Notification bucket", notificationListOfUsers[userId].length);
}

function decodeHtmlEntities(html) {
    return html.replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");
}

export function markAsReadNotification(newNotification){
    let notificationArray = [];
    if(newNotification)
    notificationArray = newNotification;
    if(store.has('notificationListOfUsers')){
        console.log('markAsReadNotification')
        const _notificationListOfUsers = store.get('notificationListOfUsers');
        console.log('markAsReadNotification:length>>>',_notificationListOfUsers[userId]?.length);
        if(_notificationListOfUsers[userId]?.length > 0){
            notificationArray = notificationArray.concat(_notificationListOfUsers[userId]);
        }
    }
    if(notificationArray.length > 0)
    mainWindow?.webContents.send(channelWindow.markAsReadNotification, JSON.stringify(notificationArray));
}

export function getNotificationList(){
    if(userId)
        return notificationListOfUsers[userId];
    return [];
} 

export function showNotification(notificationSchema , $event = null) {
    const currentIsDark = nativeTheme.shouldUseDarkColors;
    const iconPath = path.join(__dirname, '..', '..', 'public',currentIsDark?'asset/notification-icon-win-os-dark.png':'asset/notification-icon-win-os-light.png');
    const isDuplicateNotification = notificationListOfUsers[userId]?.some((_notification) => _notification.notificationId === notificationSchema.notificationId);
    if(!isDuplicateNotification){
        let notification;
        if(process.platform === 'win32') notification = createWindowsNotification(notificationSchema, iconPath);
        else notification = createMacNotification(notificationSchema, iconPath);
        
        notification.notificationId = notificationSchema.notificationId;
        notification.show();
        notificationListOfUsers[userId].push(notification);
    }
}

const createActionUrl = (navigationSchema, notificatonId) => {
    let actionUrl = `bryzos://${userId}/${notificatonId}/`;
    if(navigationSchema){
        if(navigationSchema.routePath)actionUrl+=`${navigationSchema.routePath}`;
        const state = navigationSchema.state;
        if(state){
            let stateUrl = '?';
            for(const key in state){
                stateUrl+=`${key}=${state[key]},`;
            }
            actionUrl+=`${stateUrl.substring(0,stateUrl.length-1)}`;
        }
    }
    return actionUrl;
}

const createWindowsNotification = (notificationSchema, iconPath) => {
    const title = notificationSchema.title;
    let body = notificationSchema.body;
    
    // Check if body is a JSON string and parse it if it is
    try {
        const parsedBody = JSON.parse(body);
        if (typeof parsedBody === 'object' && parsedBody !== null) {
            // If it's an object, extract the relevant text content
            if (parsedBody.text) {
                body = parsedBody.text;
            } else if (parsedBody.type === "attachment") {
                body = `File received ${parsedBody.name}.${parsedBody.extension}`;
            } else {
                // If it's an object but no specific text field, stringify it
                body = JSON.stringify(parsedBody);
            }
        }
    } catch (error) {
        // If parsing fails, use the original body as a string
        body = notificationSchema.body;
    }
    
    const navigationSchema = notificationSchema.navigation;
    const actionUrl = createActionUrl(navigationSchema, notificationSchema.notificationId);

    // Generate dynamic actions based on button array
    let actionsXml = '';
    let actionsSection = '';

    if (notificationSchema.button && Array.isArray(notificationSchema.button)) {
        notificationSchema.button.forEach((button, index) => {
            const buttonActionUrl = `${actionUrl};action=${button.action_identifier}`;
            actionsXml += `<action
                content="${button.title}"
                arguments="${buttonActionUrl}"
                activationType="protocol"/>`;
        });
        actionsSection = `<actions>
            ${actionsXml}
        </actions>`;
    }
    
    const toastXml = `<toast activationType="protocol"  launch="${actionUrl}">
            <visual>
            <binding template="ToastImageAndText02">
                <image id="1" src="file://${iconPath}" alt="Image" />
                <text id="1">${title}</text>
                <text id="2">${body}</text>
            </binding>
            </visual>
            ${actionsSection}
        </toast>`;
    
    const notification = new Notification({
        appId: 'com.squirrel.bryzos_extended_pricing_widget.bryzos',
        toastXml: toastXml
    });
    
    return notification;
}

const createMacNotification = (notificationSchema, iconPath)=> {
    const title = decodeHtmlEntities(notificationSchema.title);
    let body = decodeHtmlEntities(notificationSchema.body);
    
    // Check if body is a JSON string and parse it if it is
    try {
        const parsedBody = JSON.parse(body);
        if (typeof parsedBody === 'object' && parsedBody !== null) {
            // If it's an object, extract the relevant text content
            if (parsedBody.text) {
                body = parsedBody.text;
            } else if (parsedBody.type === "attachment") {
                body = `File received ${parsedBody.name}.${parsedBody.extension}`;
            } else {
                // If it's an object but no specific text field, stringify it
                body = JSON.stringify(parsedBody);
            }
        }
    } catch (error) {
        // If parsing fails, use the original body as a string
        body = decodeHtmlEntities(notificationSchema.body);
    }
    
    const navigationSchema = notificationSchema.navigation;
    const actionUrl = createActionUrl(navigationSchema, notificationSchema.notificationId);
    
    // Generate dynamic actions based on button array
    const actions = [];
    if (notificationSchema.button && Array.isArray(notificationSchema.button)) {
        notificationSchema.button.forEach((button) => {
            actions.push({ type: 'button', text: button.title });
        });
    }
    
    const notification = new Notification({
        title: notificationSchema.title,
        body: body,
        actions: actions.length > 0 ? actions : undefined,
        closeButtonText: 'Close',
        silent:false,
        timeoutType:'default',
        urgency:'normal',
        icon:iconPath
      });
    
      // When a button is clicked
    notification.on('click', () => {
        if (mainWindow) {
            mainWindow.webContents.send(channelWindow.handleURI, actionUrl);
            if (mainWindow.isMinimized()) mainWindow.restore();
            if (!mainWindow.isVisible()) {
                mainWindow.show();
            }
        }
    });
      notification.on('action', (event, index) => {
        if (notificationSchema.button && Array.isArray(notificationSchema.button) && notificationSchema.button[index]) {
          const button = notificationSchema.button[index];
          const url = `${actionUrl};action=${button.action_identifier}`;
          if (mainWindow) {
            mainWindow.webContents.send(channelWindow.handleURI, url);
            if (mainWindow.isMinimized()) mainWindow.restore();
            if (!mainWindow.isVisible()) {
                mainWindow.show();
            }
        }
        }
      });
    
      return notification;
}

export const notificationHandler = (notification , $event = null) => {
    showNotification(notification , $event);
    mainWindow?.webContents.send(channelWindow.markAsReadNotification, JSON.stringify([notification]));
}

export const notificationEventsHandler = (channel, channelEvents) => {
    channelEvents.forEach($event => {
        channel.bind( $event, (data) => {
            notificationHandler(data.notification , $event);
        });
    });
}